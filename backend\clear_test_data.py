#!/usr/bin/env python3
"""
清理测试数据脚本
删除数据库中所有的发货单、退货单、对账单和相关数据
"""

import sqlite3
import os
from datetime import datetime

def clear_test_data():
    """清理测试数据"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始清理测试数据...")
        
        # 1. 删除应收账款明细
        print("1. 删除应收账款明细...")
        cursor.execute("DELETE FROM receivable_details")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条应收账款明细记录")
        
        # 2. 重置应收账款金额
        print("2. 重置应收账款金额...")
        cursor.execute("UPDATE receivables SET amount = 0.00, updated_at = CURRENT_TIMESTAMP")
        updated_count = cursor.rowcount
        print(f"   重置了 {updated_count} 个客户的应收账款金额")
        
        # 3. 删除对账单收款记录
        print("3. 删除对账单收款记录...")
        cursor.execute("DELETE FROM statement_payments")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条收款记录")
        
        # 4. 删除对账单关联的退货单
        print("4. 删除对账单关联的退货单...")
        cursor.execute("DELETE FROM statement_return_orders")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条对账单退货单关联记录")
        
        # 5. 删除对账单关联的发货单
        print("5. 删除对账单关联的发货单...")
        cursor.execute("DELETE FROM statement_delivery_notes")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条对账单发货单关联记录")
        
        # 6. 删除对账单
        print("6. 删除对账单...")
        cursor.execute("DELETE FROM statements")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条对账单记录")
        
        # 7. 删除退货单明细
        print("7. 删除退货单明细...")
        cursor.execute("DELETE FROM return_order_items")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条退货单明细记录")
        
        # 8. 删除退货单
        print("8. 删除退货单...")
        cursor.execute("DELETE FROM return_orders")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条退货单记录")
        
        # 9. 删除发货单明细
        print("9. 删除发货单明细...")
        cursor.execute("DELETE FROM delivery_note_items")
        deleted_count = cursor.rowcount
        print(f"   删除了 {deleted_count} 条发货单明细记录")
        
        # 10. 删除发货单
        print("10. 删除发货单...")
        cursor.execute("DELETE FROM delivery_notes")
        deleted_count = cursor.rowcount
        print(f"    删除了 {deleted_count} 条发货单记录")
        
        # 11. 重置订单产品的发货数量
        print("11. 重置订单产品发货数量...")
        cursor.execute("""
            UPDATE order_products
            SET delivered_quantity = 0,
                updated_at = CURRENT_TIMESTAMP
            WHERE delivered_quantity > 0
        """)
        updated_count = cursor.rowcount
        print(f"    重置了 {updated_count} 个产品的发货数量")
        
        conn.commit()
        print("✅ 测试数据清理完成！")
        
        # 13. 验证清理结果
        print("\n📊 清理结果验证:")
        
        tables_to_check = [
            ('statements', '对账单'),
            ('delivery_notes', '发货单'),
            ('return_orders', '退货单'),
            ('receivable_details', '应收账款明细'),
            ('statement_payments', '收款记录')
        ]
        
        for table, name in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{name}: {count} 条记录")
        
        # 检查应收账款总额
        cursor.execute("SELECT SUM(amount) FROM receivables")
        total_receivables = cursor.fetchone()[0] or 0
        print(f"应收账款总额: ¥{total_receivables:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = clear_test_data()
    if success:
        print("\n🎉 测试数据清理成功！现在可以进行新的测试了。")
    else:
        print("\n💥 测试数据清理失败！")

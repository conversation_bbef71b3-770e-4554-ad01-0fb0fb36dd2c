"""
系统设置相关模型
包括系统设置、公司信息、银行账户、文档模板、备份记录
"""
from datetime import datetime
from app import db
from app.models.base import BaseModel


class SystemSetting(BaseModel):
    """系统设置模型"""
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False, comment='设置键')
    value = db.Column(db.Text, nullable=True, comment='设置值')
    description = db.Column(db.Text, comment='设置描述')

    def __repr__(self):
        return f'<SystemSetting {self.key}>'

    def to_dict(self):
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class CompanyInfo(BaseModel):
    """企业信息模型"""
    __tablename__ = 'company_info'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='企业名称')
    tax_id = db.Column(db.String(50), nullable=False, comment='统一社会信用代码')
    contact = db.Column(db.String(50), nullable=True, comment='联系人')
    phone = db.Column(db.String(20), nullable=True, comment='联系电话')
    email = db.Column(db.String(100), nullable=True, comment='电子邮箱')
    fax = db.Column(db.String(20), nullable=True, comment='传真')
    address = db.Column(db.String(200), nullable=True, comment='公司地址')
    logo = db.Column(db.String(255), nullable=True, comment='公司Logo')
    license_image = db.Column(db.String(255), nullable=True, comment='营业执照图片')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'tax_id': self.tax_id,
            'contact': self.contact,
            'phone': self.phone,
            'email': self.email,
            'fax': self.fax,
            'address': self.address,
            'logo': self.logo,
            'license_image': self.license_image,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class CompanyBankAccount(BaseModel):
    """企业银行账户模型"""
    __tablename__ = 'company_bank_accounts'

    id = db.Column(db.Integer, primary_key=True)
    bank_name = db.Column(db.String(100), nullable=False, comment='开户行名称')
    account_name = db.Column(db.String(100), nullable=False, comment='账户名称')
    account_number = db.Column(db.String(50), nullable=False, comment='账号')
    is_default = db.Column(db.Boolean, default=False, nullable=False, comment='是否默认账户')
    notes = db.Column(db.Text, nullable=True, comment='备注')

    def to_dict(self):
        return {
            'id': self.id,
            'bank_name': self.bank_name,
            'account_name': self.account_name,
            'account_number': self.account_number,
            'is_default': self.is_default,
            'notes': self.notes,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class DocumentTemplate(BaseModel):
    """单据模板模型"""
    __tablename__ = 'document_templates'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='模板名称')
    type = db.Column(db.String(50), nullable=False, comment='模板类型(报价单/订单/送货单等)')
    content = db.Column(db.Text, nullable=True, comment='模板内容')
    is_default = db.Column(db.Boolean, default=False, nullable=False, comment='是否默认模板')
    status = db.Column(db.String(20), default='启用', nullable=False, comment='状态(启用/禁用)')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'content': self.content,
            'is_default': self.is_default,
            'status': self.status,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }


class BackupRecord(BaseModel):
    """备份记录模型"""
    __tablename__ = 'backup_records'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False, comment='备份文件名')
    file_size = db.Column(db.Integer, nullable=False, comment='文件大小(字节)')
    backup_type = db.Column(db.String(20), nullable=False, comment='备份类型(手动/自动)')
    description = db.Column(db.String(200), nullable=True, comment='备份描述')

    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'file_size': self.file_size,
            'backup_type': self.backup_type,
            'description': self.description,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

"""
API v1模块初始化文件
注册所有API命名空间到Flask-RESTX
"""
from flask_restx import Api

# 导入所有API命名空间
from .customers import api as customers_api
from .products import api as products_api
from .quotations import api as quotations_api
from .orders import api as orders_api
from .delivery_notes import api as delivery_notes_api
from .returns import api as returns_api
from .statements import api as statements_api
from .finance import api as finance_api
from .payments import api as payments_api
from .dashboard import api as dashboard_api
from .system import api as system_api
from .uploads import api as uploads_api
from .error_logs import api as error_logs_api
from .order_export import api as order_export_api
from .order_print import api as order_print_api
from .product_images import api as product_images_api
from .statement_refunds import api as statement_refunds_api

def register_api_namespaces(api: Api):
    """注册所有API命名空间"""
    # 核心业务模块
    api.add_namespace(customers_api, path='/customers')
    api.add_namespace(products_api, path='/products')
    api.add_namespace(quotations_api, path='/quotations')
    api.add_namespace(orders_api, path='/orders')
    api.add_namespace(delivery_notes_api, path='/delivery-notes')
    api.add_namespace(returns_api, path='/returns')
    api.add_namespace(statements_api, path='/statements')
    api.add_namespace(finance_api, path='/finance')
    api.add_namespace(payments_api, path='/payments')

    # 系统管理模块
    api.add_namespace(dashboard_api, path='/dashboard')
    api.add_namespace(system_api, path='/system')

    # 扩展功能模块
    api.add_namespace(uploads_api, path='/uploads')
    api.add_namespace(error_logs_api, path='/error-logs')
    api.add_namespace(order_export_api, path='/order-export')
    api.add_namespace(order_print_api, path='/order-print')
    api.add_namespace(product_images_api, path='/product-images')
    api.add_namespace(statement_refunds_api, path='/statement-refunds')

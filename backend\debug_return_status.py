#!/usr/bin/env python3
"""
调试退货单状态问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.api.v1.returns import RETURN_ORDER_STATUSES

def debug_status():
    """调试状态定义"""
    print("🔍 退货单状态调试")
    print("=" * 50)
    
    print("📋 定义的状态列表:")
    for i, status in enumerate(RETURN_ORDER_STATUSES):
        print(f"  {i+1}. '{status}' (长度: {len(status)}, 字节: {status.encode('utf-8')})")
    
    print()
    print("🧪 测试状态匹配:")
    test_status = "已完成"
    print(f"测试状态: '{test_status}' (长度: {len(test_status)}, 字节: {test_status.encode('utf-8')})")
    print(f"是否在列表中: {test_status in RETURN_ORDER_STATUSES}")
    
    print()
    print("🔍 逐个比较:")
    for status in RETURN_ORDER_STATUSES:
        match = status == test_status
        print(f"  '{status}' == '{test_status}': {match}")
        if not match:
            print(f"    状态字节: {status.encode('utf-8')}")
            print(f"    测试字节: {test_status.encode('utf-8')}")

if __name__ == '__main__':
    debug_status()

# EMB工程物资报价及订单管理系统

## 📋 项目概述

EMB系统是一个专业的工程物资报价及订单管理系统，为工程公司提供完整的业务流程管理解决方案。系统采用现代化的技术架构，支持从客户管理、产品管理、报价处理到订单执行、财务管理的全流程数字化管理。

### 🎯 核心功能

- **客户管理**：客户信息维护、银行账户管理、送货地址管理
- **产品管理**：产品分类、规格管理、库存跟踪
- **报价管理**：报价需求处理、报价单生成、状态流转管理
- **订单管理**：订单处理、发货单管理、物流跟踪
- **财务管理**：收款记录、退款处理、对账单生成
- **系统管理**：企业信息配置、系统设置、数据统计

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   后端服务      │
│   (React/Vue)   │◄──►│   (Nginx)       │◄──►│   (Flask)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   数据库        │
                                               │   (PostgreSQL)  │
                                               └─────────────────┘
```

### 🛠️ 技术栈

**后端技术**
- **框架**: Flask 2.3.3 + Flask-RESTX
- **数据库**: SQLAlchemy ORM + PostgreSQL/MySQL/SQLite
- **数据验证**: Marshmallow
- **API文档**: Swagger/OpenAPI 3.0
- **测试**: pytest + coverage
- **部署**: Gunicorn + Docker + Nginx

**开发工具**
- **版本控制**: Git + GitHub
- **代码质量**: pytest + coverage
- **容器化**: Docker + Docker Compose
- **CI/CD**: GitHub Actions (可扩展)

## 🚀 快速开始

### 环境要求

- Python 3.8+
- pip 20.0+
- Git 2.0+
- 数据库：PostgreSQL 12+ / MySQL 8.0+ / SQLite 3.x

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/kkkdkk/EMB-new.git
cd EMB-new/backend
```

2. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件（修改数据库连接等）
# 推荐使用VS Code或其他编辑器
```

3. **安装依赖**
```bash
# Windows
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt

# Linux/macOS
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

4. **初始化数据库**
```bash
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all(); print('数据库初始化完成')"
```

5. **启动服务**
```bash
python run.py
```

6. **访问应用**
- API文档: http://localhost:5001/docs/
- 健康检查: http://localhost:5001/test/health

### 🐳 Docker快速部署

```bash
# 单容器部署
docker build -t emb-system .
docker run -d -p 5001:5001 --name emb-api emb-system

# 完整服务栈部署
docker-compose up -d
```

## 📚 API文档

### 核心API端点

| 模块 | 端点 | 描述 |
|------|------|------|
| 客户管理 | `/api/v1/customers` | 客户CRUD、银行账户、送货地址 |
| 产品管理 | `/api/v1/products` | 产品、分类、规格管理 |
| 报价管理 | `/api/v1/quotations` | 报价需求、报价单处理 |
| 订单管理 | `/api/v1/orders` | 订单、发货单管理 |
| 财务管理 | `/api/v1/finance` | 收款、退款、对账管理 |
| 系统管理 | `/api/v1/system` | 企业信息、系统设置 |

### API特性

- **RESTful设计**: 遵循REST API设计规范
- **统一响应格式**: 标准化的JSON响应结构
- **完整文档**: Swagger/OpenAPI 3.0自动生成
- **数据验证**: 请求参数和响应数据验证
- **错误处理**: 统一的错误码和错误信息
- **分页支持**: 列表接口支持分页、排序、筛选

### 响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🗄️ 数据库设计

### 核心数据表

```sql
-- 客户表
customers (id, name, contact, phone, email, address, status, level, ...)

-- 产品表
products (id, name, model, unit, category_id, description, status, ...)
product_categories (id, name, description, parent_id, level, ...)
product_specifications (id, product_id, specification, cost_price, ...)

-- 报价表
quotation_requests (id, customer_id, project_name, status, ...)
quotations (id, request_id, customer_id, quotation_number, ...)
quotation_items (id, quotation_id, product_id, quantity, price, ...)

-- 订单表
orders (id, order_number, customer_id, quotation_id, status, ...)
order_products (id, order_id, product_id, quantity, unit_price, ...)
delivery_notes (id, order_id, delivery_number, delivery_date, ...)

-- 财务表
payment_records (id, order_id, amount, payment_method, status, ...)
refund_records (id, return_order_id, amount, refund_method, ...)
statements (id, customer_id, statement_number, total_amount, ...)
```

### 数据关系

- 客户 → 报价需求 → 报价单 → 订单 → 发货单
- 产品 → 产品规格 → 报价项目/订单产品
- 订单 → 收款记录 → 对账单
- 订单 → 退货单 → 退款记录

## 🧪 测试指南

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_customers.py -v

# 运行覆盖率测试
python -m pytest --cov=app tests/ --cov-report=html

# 使用测试脚本
python run_tests.py
```

### 测试结构

```
tests/
├── conftest.py              # 测试配置和夹具
├── test_customers.py        # 客户管理测试
├── test_products.py         # 产品管理测试
├── test_integration.py      # 集成测试
└── ...
```

### 测试覆盖

- **单元测试**: API端点、业务逻辑、数据验证
- **集成测试**: 完整业务流程测试
- **数据库测试**: 模型关系和约束测试
- **错误处理测试**: 异常情况和边界条件

## 🚀 部署指南

### 开发环境

```bash
# 使用部署脚本
./deploy.sh --env dev

# 手动部署
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python run.py
```

### 生产环境

```bash
# 使用部署脚本
./deploy.sh --env prod

# 手动部署
pip install -r requirements-prod.txt
gunicorn --config gunicorn.conf.py wsgi:application
```

### Docker部署

```bash
# 构建镜像
docker build -t emb-system .

# 运行容器
docker run -d \
  --name emb-api \
  -p 5001:5001 \
  -e DATABASE_URL=postgresql://user:pass@host/db \
  -e SECRET_KEY=your_secret_key \
  emb-system

# 使用Docker Compose
docker-compose up -d
```

详细部署说明请参考 [DEPLOYMENT.md](backend/DEPLOYMENT.md)

## 📁 项目结构

```
EMB-new/
├── backend/                 # 后端应用
│   ├── app/                # Flask应用
│   │   ├── api/           # API路由
│   │   │   └── v1/        # API版本1
│   │   ├── models/        # 数据模型
│   │   ├── schemas/       # 数据验证
│   │   └── utils/         # 工具函数
│   ├── tests/             # 测试文件
│   ├── config.py          # 配置管理
│   ├── run.py             # 开发服务器
│   ├── wsgi.py            # 生产入口
│   ├── requirements.txt   # 依赖列表
│   └── ...
├── .taskmaster/           # 任务管理
└── README.md             # 项目文档
```

## 🔧 开发指南

### 代码规范

- **PEP 8**: Python代码风格规范
- **类型提示**: 使用Python类型注解
- **文档字符串**: 函数和类的详细文档
- **错误处理**: 统一的异常处理机制

### API开发流程

1. **定义数据模型** (models/)
2. **创建数据验证** (schemas/)
3. **实现API端点** (api/v1/)
4. **编写测试用例** (tests/)
5. **更新API文档** (自动生成)

### 数据库迁移

```bash
# 使用Flask-Migrate（推荐）
flask db init
flask db migrate -m "描述"
flask db upgrade

# 直接创建表
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

## 🔍 监控和维护

### 健康检查

- **端点**: `/test/health`
- **监控**: 应用状态、数据库连接、系统资源

### 日志管理

- **开发环境**: 控制台输出
- **生产环境**: 文件轮转 (logs/app.log)
- **日志级别**: DEBUG, INFO, WARNING, ERROR

### 性能优化

- **数据库**: 索引优化、查询优化
- **缓存**: Redis缓存支持
- **并发**: Gunicorn多worker
- **静态文件**: Nginx静态文件服务

## 🤝 贡献指南

### 开发流程

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 提交规范

```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

类型：feat, fix, docs, style, refactor, test, chore

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目地址**: https://github.com/kkkdkk/EMB-new
- **问题反馈**: https://github.com/kkkdkk/EMB-new/issues
- **文档**: 项目Wiki页面

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

## 📈 功能特性详解

### 客户管理模块

**核心功能**
- 客户基本信息管理（公司名称、联系人、电话、邮箱等）
- 客户分级管理（A/B/C级客户）
- 客户状态管理（正常/禁用/黑名单）
- 银行账户信息管理
- 送货地址管理（支持多个地址）

**API端点**
```
GET    /api/v1/customers              # 获取客户列表
POST   /api/v1/customers              # 创建客户
GET    /api/v1/customers/{id}         # 获取客户详情
PUT    /api/v1/customers/{id}         # 更新客户信息
DELETE /api/v1/customers/{id}         # 删除客户
GET    /api/v1/customers/{id}/bank-accounts    # 获取银行账户
POST   /api/v1/customers/{id}/bank-accounts    # 添加银行账户
```

### 产品管理模块

**核心功能**
- 产品分类管理（支持多级分类）
- 产品基本信息管理（名称、型号、单位、描述等）
- 产品规格管理（规格描述、成本价、建议价、税率）
- 产品状态管理（正常/停用）

**API端点**
```
GET    /api/v1/products               # 获取产品列表
POST   /api/v1/products               # 创建产品
GET    /api/v1/products/{id}          # 获取产品详情
PUT    /api/v1/products/{id}          # 更新产品信息
DELETE /api/v1/products/{id}          # 删除产品
GET    /api/v1/products/categories    # 获取分类列表
POST   /api/v1/products/categories    # 创建分类
```

### 报价管理模块

**业务流程**
1. 客户提交报价需求
2. 业务员处理需求，生成报价单
3. 报价单审核和确认
4. 客户确认后转为订单

**核心功能**
- 报价需求管理（项目信息、产品需求、期望交期）
- 报价单生成（基于需求自动生成或手动创建）
- 报价单状态流转（草稿→待审核→已确认→已转订单）
- 报价单打印和导出

**API端点**
```
GET    /api/v1/quotations/requests    # 获取报价需求列表
POST   /api/v1/quotations/requests    # 创建报价需求
POST   /api/v1/quotations/requests/{id}/generate-quotation  # 生成报价单
GET    /api/v1/quotations             # 获取报价单列表
PUT    /api/v1/quotations/{id}/status # 更新报价单状态
```

### 订单管理模块

**业务流程**
1. 基于确认的报价单创建订单
2. 订单确认和生产安排
3. 发货单生成和物流管理
4. 订单状态跟踪

**核心功能**
- 订单创建（手动创建或基于报价单）
- 订单状态管理（待确认→已确认→生产中→待发货→发货中→已完成）
- 发货单管理（发货计划、物流信息、签收确认）
- 订单产品管理（数量、价格、已发货数量）

**API端点**
```
GET    /api/v1/orders                 # 获取订单列表
POST   /api/v1/orders                 # 创建订单
POST   /api/v1/orders/quotations/{id}/create-order  # 基于报价单创建订单
PUT    /api/v1/orders/{id}/status     # 更新订单状态
GET    /api/v1/orders/delivery-notes  # 获取发货单列表
POST   /api/v1/orders/delivery-notes  # 创建发货单
```

### 财务管理模块

**核心功能**
- 收款记录管理（银行转账、现金、在线支付等）
- 退款记录管理（基于退货单的退款处理）
- 对账单生成（基于发货单生成对账单）
- 应收款统计和分析

**API端点**
```
GET    /api/v1/finance/payment-records    # 获取收款记录
POST   /api/v1/finance/payment-records    # 创建收款记录
GET    /api/v1/finance/refund-records     # 获取退款记录
POST   /api/v1/finance/refund-records     # 创建退款记录
GET    /api/v1/finance/statements         # 获取对账单列表
POST   /api/v1/finance/statements         # 创建对账单
```

## 🔧 高级配置

### 环境变量配置

```bash
# 应用基础配置
FLASK_ENV=production                    # 运行环境
SECRET_KEY=your_super_secret_key       # 应用密钥
PORT=5001                              # 服务端口

# 数据库配置
DATABASE_URL=********************************/emb_system

# 缓存配置
CACHE_TYPE=redis
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_CONTENT_LENGTH=16777216            # 16MB
UPLOAD_FOLDER=uploads

# 邮件配置（可选）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 安全配置
SESSION_COOKIE_SECURE=True             # 生产环境启用
WTF_CSRF_ENABLED=True                  # CSRF保护
```

### 数据库配置

**PostgreSQL (推荐生产环境)**
```bash
DATABASE_URL=postgresql://username:password@localhost:5432/emb_system
```

**MySQL**
```bash
DATABASE_URL=mysql://username:password@localhost:3306/emb_system
```

**SQLite (开发环境)**
```bash
DATABASE_URL=sqlite:///project.db
```

### 性能调优

**Gunicorn配置** (gunicorn.conf.py)
```python
# Worker配置
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "gevent"
worker_connections = 1000

# 性能配置
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
```

**数据库连接池**
```python
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 10,
    'pool_timeout': 20,
    'pool_recycle': 3600,
    'max_overflow': 20
}
```

## 🔐 安全最佳实践

### 1. 密钥管理
- 使用强随机密钥
- 定期轮换密钥
- 不在代码中硬编码密钥

### 2. 数据库安全
- 使用参数化查询防止SQL注入
- 限制数据库用户权限
- 启用数据库连接加密

### 3. API安全
- 启用CSRF保护
- 实施请求频率限制
- 输入数据验证和清理

### 4. 部署安全
- 使用HTTPS加密传输
- 配置防火墙规则
- 定期更新系统和依赖

## 📊 监控和运维

### 应用监控

**健康检查端点**
```bash
curl http://localhost:5001/test/health
```

**响应示例**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "uptime": "2 days, 3 hours, 45 minutes"
}
```

### 日志监控

**日志级别**
- DEBUG: 详细调试信息
- INFO: 一般信息记录
- WARNING: 警告信息
- ERROR: 错误信息

**日志格式**
```
2024-01-01 12:00:00,000 INFO [app.api.customers] 客户创建成功: ID=123
2024-01-01 12:00:01,000 ERROR [app.api.orders] 订单创建失败: 客户不存在
```

### 性能监控

**关键指标**
- 响应时间: API端点平均响应时间
- 吞吐量: 每秒处理请求数
- 错误率: 4xx/5xx错误比例
- 数据库连接: 连接池使用情况

**监控工具推荐**
- Prometheus + Grafana
- ELK Stack (Elasticsearch + Logstash + Kibana)
- Sentry (错误追踪)

## 🚨 故障排除

### 常见问题

**1. 端口被占用**
```bash
# 查看端口使用情况
netstat -tulpn | grep 5001

# 修改端口
export PORT=5002
python run.py
```

**2. 数据库连接失败**
```bash
# 检查数据库服务
sudo systemctl status postgresql

# 测试连接
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); print('数据库连接成功' if db.engine.execute('SELECT 1').scalar() == 1 else '连接失败')"
```

**3. 依赖安装失败**
```bash
# 升级pip
pip install --upgrade pip

# 清理缓存
pip cache purge

# 重新安装
pip install -r requirements.txt --force-reinstall
```

**4. 权限问题**
```bash
# 修改文件权限
chmod -R 755 uploads/
chown -R www-data:www-data uploads/
```

### 调试技巧

**1. 启用调试模式**
```bash
export FLASK_ENV=development
export FLASK_DEBUG=True
python run.py
```

**2. 查看详细日志**
```bash
export LOG_LEVEL=DEBUG
tail -f logs/app.log
```

**3. 数据库调试**
```python
# 启用SQL查询日志
app.config['SQLALCHEMY_ECHO'] = True
```

## 🔄 版本更新

### 更新流程

1. **备份数据**
```bash
# 备份数据库
pg_dump emb_system > backup_$(date +%Y%m%d).sql

# 备份文件
tar -czf backup_files_$(date +%Y%m%d).tar.gz uploads/ logs/
```

2. **拉取更新**
```bash
git pull origin master
```

3. **更新依赖**
```bash
pip install -r requirements.txt --upgrade
```

4. **数据库迁移**
```bash
flask db upgrade
```

5. **重启服务**
```bash
sudo systemctl restart emb-api
```

### 版本历史

- **v1.0.0**: 初始版本，包含核心功能模块
- **v1.1.0**: 添加财务管理模块
- **v1.2.0**: 优化API性能，添加缓存支持
- **v1.3.0**: 增强安全性，添加审计日志

**EMB工程物资报价及订单管理系统** - 让工程业务管理更简单、更高效！

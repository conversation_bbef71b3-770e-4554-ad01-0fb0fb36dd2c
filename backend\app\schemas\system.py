"""
系统管理相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any


class SystemSettingSchema(Schema):
    """系统设置序列化模式"""
    id = fields.Int(dump_only=True)
    key = fields.Str(required=True, validate=validate.Length(max=50))
    value = fields.Str(allow_none=True)
    description = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class CompanyInfoSchema(Schema):
    """企业信息序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "企业名称不能为空"}
    )
    tax_id = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=50), 
        error_messages={"required": "统一社会信用代码不能为空"}
    )
    contact = fields.String(validate=validate.Length(max=50), allow_none=True)
    phone = fields.String(validate=validate.Length(max=20), allow_none=True)
    email = fields.String(validate=validate.Length(max=100), allow_none=True)
    fax = fields.String(validate=validate.Length(max=20), allow_none=True)
    address = fields.String(validate=validate.Length(max=200), allow_none=True)
    logo = fields.String(validate=validate.Length(max=255), allow_none=True)
    license_image = fields.String(validate=validate.Length(max=255), allow_none=True)
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class CompanyBankAccountSchema(Schema):
    """企业银行账户序列化模式"""
    id = fields.Integer(dump_only=True)
    bank_name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "开户行名称不能为空"}
    )
    account_name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "账户名称不能为空"}
    )
    account_number = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=50), 
        error_messages={"required": "账号不能为空"}
    )
    is_default = fields.Boolean(load_default=False)
    notes = fields.String(allow_none=True)
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class DocumentTemplateSchema(Schema):
    """单据模板序列化模式"""
    id = fields.Integer(dump_only=True)
    name = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=100), 
        error_messages={"required": "模板名称不能为空"}
    )
    type = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=50), 
        error_messages={"required": "模板类型不能为空"}
    )
    content = fields.String(allow_none=True)
    is_default = fields.Boolean(load_default=False)
    status = fields.String(
        validate=validate.OneOf(["启用", "禁用"]), 
        load_default="启用"
    )
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")
    updated_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")

    @validates("type")
    def validate_type(self, value):
        """验证模板类型"""
        valid_types = ["报价单", "订单", "送货单", "对账单", "退货单"]
        if value not in valid_types:
            raise ValidationError(f"模板类型必须是以下之一: {', '.join(valid_types)}")


class BackupRecordSchema(Schema):
    """备份记录序列化模式"""
    id = fields.Integer(dump_only=True)
    filename = fields.String(
        required=True, 
        validate=validate.Length(min=1, max=255), 
        error_messages={"required": "备份文件名不能为空"}
    )
    file_size = fields.Integer(
        required=True, 
        error_messages={"required": "文件大小不能为空"}
    )
    backup_type = fields.String(
        required=True, 
        validate=validate.OneOf(["手动", "自动"]), 
        error_messages={"required": "备份类型不能为空"}
    )
    description = fields.String(validate=validate.Length(max=200), allow_none=True)
    created_at = fields.DateTime(dump_only=True, format="%Y-%m-%d %H:%M:%S")


class ErrorLogSchema(Schema):
    """错误日志序列化模式"""
    id = fields.Int(dump_only=True)
    # 兼容旧版本的level字段
    level = fields.Str(allow_none=True)
    # 新版本使用log_type字段
    log_type = fields.Str(allow_none=True)
    message = fields.Str(required=True)
    # 旧版本字段
    details = fields.Str(allow_none=True)
    # 新版本用这个字段
    stack_trace = fields.Str(allow_none=True)
    # 额外信息
    additional_info = fields.Raw(allow_none=True)
    # 旧版本字段
    origin = fields.Str(allow_none=True)
    user_agent = fields.Str(allow_none=True)
    ip_address = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class AutoBackupSettingsSchema(Schema):
    """自动备份设置验证模式"""
    enabled = fields.Boolean(required=True)
    frequency = fields.String(
        required=True, 
        validate=validate.OneOf(['daily', 'weekly', 'monthly'])
    )
    time = fields.String(required=True)  # ISO格式的时间字符串
    keepCount = fields.Integer(
        required=True, 
        validate=validate.Range(min=1, max=50)
    )
    path = fields.String(required=True)

#!/usr/bin/env python3
"""
调试退货单完成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.return_order import ReturnOrder

def debug_return_complete():
    """调试退货单完成功能"""
    app = create_app()
    
    with app.app_context():
        print("🔍 调试退货单完成功能")
        print("=" * 50)
        
        try:
            # 获取退货单
            return_order = ReturnOrder.query.get(3)
            if not return_order:
                print("❌ 退货单不存在")
                return
            
            print(f"📋 退货单信息:")
            print(f"   ID: {return_order.id}")
            print(f"   编号: {return_order.return_number}")
            print(f"   当前状态: {return_order.status}")
            print(f"   订单ID: {return_order.order_id}")
            print(f"   项目数量: {len(return_order.items) if return_order.items else 0}")
            
            if return_order.order:
                print(f"   关联订单: {return_order.order.order_number}")
                print(f"   客户ID: {return_order.order.customer_id}")
            
            print()
            print("🧪 测试状态更新:")
            
            # 测试状态更新
            old_status = return_order.status
            new_status = "已完成"
            
            print(f"   从 '{old_status}' 更新到 '{new_status}'")
            
            # 检查是否会触发应收账款逻辑
            will_add_to_receivables = (old_status != '已完成' and new_status == '已完成')
            print(f"   是否会添加到应收账款: {will_add_to_receivables}")
            
            if will_add_to_receivables:
                print()
                print("💰 测试应收账款计算:")
                
                # 测试计算退货单金额
                if return_order.items:
                    for i, item in enumerate(return_order.items):
                        print(f"   项目 {i+1}:")
                        print(f"     数量: {item.quantity}")
                        print(f"     订单产品ID: {item.order_product_id}")
                        
                        if item.order_product:
                            print(f"     单价: {item.order_product.unit_price}")
                            print(f"     折扣: {item.order_product.discount}%")
                            print(f"     税率: {item.order_product.tax_rate}%")
                            
                            # 计算金额
                            from decimal import Decimal
                            unit_price = Decimal(str(item.order_product.unit_price or 0))
                            discount_rate = Decimal(str(item.order_product.discount or 0)) / Decimal('100')
                            tax_rate = Decimal(str(item.order_product.tax_rate or 0)) / Decimal('100')
                            quantity = Decimal(str(item.quantity or 0))

                            actual_price = unit_price * (Decimal('1') - discount_rate) * (Decimal('1') + tax_rate)
                            item_amount = actual_price * quantity
                            
                            print(f"     实际单价: {actual_price}")
                            print(f"     项目金额: {item_amount}")
                        else:
                            print(f"     ❌ 订单产品不存在")
                else:
                    print("   ❌ 没有退货项目")
            
            print()
            print("🚀 执行状态更新:")
            
            # 执行状态更新
            return_order.update_status(new_status)
            db.session.commit()
            
            print("✅ 状态更新成功")
            print(f"   新状态: {return_order.status}")
            
        except Exception as e:
            print(f"❌ 调试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    debug_return_complete()

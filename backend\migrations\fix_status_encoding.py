#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复订单状态字段的中文编码问题
"""

import sqlite3
import os

def fix_encoding():
    """修复编码问题"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    
    # 使用正确的编码连接数据库
    conn = sqlite3.connect(db_path)
    conn.execute("PRAGMA encoding = 'UTF-8'")
    cursor = conn.cursor()
    
    try:
        # 获取所有订单
        cursor.execute("SELECT id, order_number, status, total_amount, paid_amount FROM orders")
        orders = cursor.fetchall()
        
        print(f"处理 {len(orders)} 个订单...")
        
        for order in orders:
            order_id, order_number, current_status, total_amount, paid_amount = order
            
            # 根据原状态映射到新状态
            if current_status == '待确认':
                order_status = '待确认'
                payment_status = '未收款'
            elif current_status == '已确认':
                order_status = '已确认'
                payment_status = '未收款'
            elif current_status == '生产中':
                order_status = '生产中'
                payment_status = '未收款'
            elif current_status == '待发货':
                order_status = '待发货'
                payment_status = '未收款'
            elif current_status == '发货中':
                order_status = '发货中'
                payment_status = '未收款'
            elif current_status == '部分发货':
                order_status = '部分发货'
                payment_status = '未收款'
            elif current_status == '全部发货':
                order_status = '全部发货'
                payment_status = '未收款'
            elif current_status == '已完成':
                order_status = '已完成'
                payment_status = '已收款'
            else:
                order_status = '待确认'
                payment_status = '未收款'
            
            # 根据金额调整财务状态
            if total_amount and paid_amount:
                total = float(total_amount)
                paid = float(paid_amount)
                
                if paid == 0:
                    payment_status = '未收款'
                elif paid < total:
                    payment_status = '部分收款'
                elif paid >= total:
                    payment_status = '已收款'
            
            # 更新状态
            cursor.execute("""
                UPDATE orders 
                SET order_status = ?, payment_status = ?
                WHERE id = ?
            """, (order_status, payment_status, order_id))
            
            print(f"订单 {order_number}: {current_status} -> 物流:{order_status}, 财务:{payment_status}")
        
        conn.commit()
        print("✅ 状态迁移完成")
        
        # 验证结果
        cursor.execute("SELECT order_status, payment_status, COUNT(*) FROM orders GROUP BY order_status, payment_status")
        results = cursor.fetchall()
        print("\n📊 迁移结果:")
        for row in results:
            print(f"物流:{row[0]}, 财务:{row[1]} - {row[2]}个订单")
            
    except Exception as e:
        conn.rollback()
        print(f"❌ 错误: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    fix_encoding()

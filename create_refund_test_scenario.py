#!/usr/bin/env python3
"""
创建退款测试场景
模拟客户多付款的情况
"""

import sqlite3
import os
from decimal import Decimal

def create_refund_scenario():
    """创建退款测试场景"""
    
    # 数据库文件路径
    db_path = os.path.join('backend', 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("创建退款测试场景...")
        
        # 1. 查看对账单12的当前状态
        cursor.execute("""
            SELECT id, statement_number, total_amount, discount_amount, 
                   adjusted_total_amount, paid_amount, status
            FROM statements 
            WHERE id = 12
        """)
        
        statement = cursor.fetchone()
        if not statement:
            print("对账单12不存在")
            return False
        
        print(f"对账单当前状态:")
        print(f"  ID: {statement[0]}")
        print(f"  编号: {statement[1]}")
        print(f"  总金额: {statement[2]}")
        print(f"  优惠金额: {statement[3]}")
        print(f"  实际金额: {statement[4]}")
        print(f"  已付金额: {statement[5]}")
        print(f"  状态: {statement[6]}")
        
        # 2. 模拟客户多付款的情况
        # 将已付金额设置为超过实际金额
        new_paid_amount = float(statement[4]) + 50.0  # 多付50元
        
        print(f"\n模拟多付款场景:")
        print(f"  实际金额: {statement[4]}")
        print(f"  设置已付金额: {new_paid_amount}")
        print(f"  需要退款: {new_paid_amount - float(statement[4])}")
        
        # 更新对账单的已付金额
        cursor.execute("""
            UPDATE statements 
            SET paid_amount = ?, status = '部分收款'
            WHERE id = 12
        """, (new_paid_amount,))
        
        # 3. 创建一个模拟的收款记录（用于解释多付款的来源）
        cursor.execute("""
            INSERT INTO statement_payments 
            (statement_id, payment_date, amount, payment_method, payment_source, 
             notes, status, created_at, updated_at)
            VALUES (12, date('now'), ?, 'bank_transfer', 'direct', 
                    '测试多付款场景', '已确认', datetime('now'), datetime('now'))
        """, (new_paid_amount,))
        
        # 4. 提交更改
        conn.commit()
        
        print("\n✅ 退款测试场景创建成功！")
        print("现在对账单12有多付款，可以测试退款功能")
        
        return True
        
    except sqlite3.Error as e:
        print(f"数据库操作失败: {e}")
        if conn:
            conn.rollback()
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("=" * 50)
    print("创建退款测试场景")
    print("=" * 50)
    
    success = create_refund_scenario()
    
    if success:
        print("\n现在可以测试退款功能了！")
        print("1. 访问 http://localhost:3002/statements/12")
        print("2. 应该能看到退款按钮")
        print("3. 点击退款按钮测试退款流程")
    else:
        print("\n❌ 创建测试场景失败！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试前端收款管理功能
验证前端页面是否能正常访问和使用
"""
import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_frontend_payment():
    """测试前端收款管理功能"""
    print("🧪 开始测试前端收款管理功能...")
    
    # 检查后端API是否可用
    try:
        response = requests.get('http://localhost:5001/api/v1/payments/payment-stats')
        if response.status_code != 200:
            print("❌ 后端API不可用，请确保后端服务正在运行")
            return False
        print("✅ 后端API可用")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端API，请确保后端服务正在运行")
        return False
    
    # 检查前端服务是否可用
    try:
        response = requests.get('http://localhost:3001')
        if response.status_code != 200:
            print("❌ 前端服务不可用，请确保前端服务正在运行")
            return False
        print("✅ 前端服务可用")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到前端服务，请确保前端服务正在运行")
        return False
    
    # 配置Chrome浏览器选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = None
    try:
        # 启动浏览器
        print("🌐 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 10)
        
        # 访问收款管理测试页面
        print("📄 访问收款管理测试页面...")
        driver.get('http://localhost:3001/test-payment')
        
        # 等待页面加载
        wait.until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
        time.sleep(3)
        
        # 检查页面标题
        page_title = driver.title
        print(f"📋 页面标题: {page_title}")
        
        # 检查是否有收款管理相关元素
        try:
            # 查找收款管理标题
            title_element = wait.until(EC.presence_of_element_located((By.TAG_NAME, 'h2')))
            if '收款管理' in title_element.text:
                print("✅ 找到收款管理标题")
            else:
                print(f"⚠️  页面标题不匹配: {title_element.text}")
        except:
            print("❌ 未找到收款管理标题")
        
        # 检查统计卡片
        try:
            stat_cards = driver.find_elements(By.CLASS_NAME, 'stat-card')
            if len(stat_cards) >= 4:
                print(f"✅ 找到 {len(stat_cards)} 个统计卡片")
            else:
                print(f"⚠️  统计卡片数量不足: {len(stat_cards)}")
        except:
            print("❌ 未找到统计卡片")
        
        # 检查标签页
        try:
            tabs = driver.find_elements(By.CLASS_NAME, 'el-tabs__item')
            if len(tabs) >= 3:
                print(f"✅ 找到 {len(tabs)} 个标签页")
                for i, tab in enumerate(tabs):
                    print(f"   标签页 {i+1}: {tab.text}")
            else:
                print(f"⚠️  标签页数量不足: {len(tabs)}")
        except:
            print("❌ 未找到标签页")
        
        # 检查操作按钮
        try:
            buttons = driver.find_elements(By.CLASS_NAME, 'el-button')
            button_texts = [btn.text for btn in buttons if btn.text.strip()]
            print(f"✅ 找到操作按钮: {button_texts}")
        except:
            print("❌ 未找到操作按钮")
        
        # 检查表格
        try:
            tables = driver.find_elements(By.CLASS_NAME, 'el-table')
            if len(tables) > 0:
                print(f"✅ 找到 {len(tables)} 个数据表格")
            else:
                print("⚠️  未找到数据表格")
        except:
            print("❌ 表格检查失败")
        
        # 截图保存
        try:
            screenshot_path = 'payment_management_screenshot.png'
            driver.save_screenshot(screenshot_path)
            print(f"📸 页面截图已保存: {screenshot_path}")
        except:
            print("⚠️  截图保存失败")
        
        print("🎉 前端收款管理功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False
    
    finally:
        if driver:
            driver.quit()
            print("🔚 浏览器已关闭")

def show_test_summary():
    """显示测试总结"""
    print("\n" + "="*60)
    print("📋 前端收款管理功能测试总结")
    print("="*60)
    print("✅ 测试内容:")
    print("   - 后端API连接测试")
    print("   - 前端服务连接测试")
    print("   - 收款管理页面加载测试")
    print("   - 页面元素检查测试")
    print("   - 统计卡片显示测试")
    print("   - 标签页功能测试")
    print("   - 操作按钮检查测试")
    print("   - 数据表格检查测试")
    print("")
    print("🔗 访问地址:")
    print("   - 前端测试页面: http://localhost:3001/test-payment")
    print("   - 后端API文档: http://localhost:5001/docs/")
    print("   - 收款API基础路径: /api/v1/payments")
    print("")
    print("💡 使用说明:")
    print("   1. 确保后端服务运行在端口5001")
    print("   2. 确保前端服务运行在端口3001")
    print("   3. 访问测试页面查看收款管理功能")
    print("   4. 可以进行余额充值、收款记录等操作")
    print("="*60)

if __name__ == '__main__':
    success = test_frontend_payment()
    show_test_summary()
    
    if success:
        print("\n✅ 前端收款管理功能测试通过！")
        print("\n🎯 下一步:")
        print("   - 可以访问 http://localhost:3001/test-payment 查看完整功能")
        print("   - 测试余额充值、直接收款等功能")
        print("   - 查看统计数据和交易记录")
    else:
        print("\n❌ 前端收款管理功能测试失败，请检查服务状态。")
        print("\n🔧 故障排除:")
        print("   - 检查后端服务: cd backend && python run.py")
        print("   - 检查前端服务: cd frontend && npm run dev")
        print("   - 确保Chrome浏览器已安装")
        exit(1)

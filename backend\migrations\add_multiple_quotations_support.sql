-- 添加多报价单支持的数据库迁移脚本（SQLite版本）
-- 执行日期: 2025-06-30
-- 数据库文件: project.db

-- 1. 备份现有数据（如果需要）
-- CREATE TABLE orders_backup AS SELECT * FROM orders;
-- CREATE TABLE order_products_backup AS SELECT * FROM order_products;

-- 2. 为 order_products 表添加来源信息字段
ALTER TABLE order_products ADD COLUMN source_type TEXT DEFAULT 'manual';
ALTER TABLE order_products ADD COLUMN source_id INTEGER;
ALTER TABLE order_products ADD COLUMN source_display TEXT;

-- 3. 添加索引提升查询性能
CREATE INDEX idx_order_products_source_type ON order_products(source_type);
CREATE INDEX idx_order_products_source_id ON order_products(source_id);

-- 4. 为 orders 表添加新的 quotation_ids 字段（JSON格式）
ALTER TABLE orders ADD COLUMN quotation_ids TEXT;

-- 5. 数据迁移：将现有的单个 quotation_id 转换为 JSON 数组格式
UPDATE orders
SET quotation_ids = '[' || quotation_id || ']'
WHERE quotation_id IS NOT NULL AND quotation_id != 0;

-- 6. 将 NULL 或 0 的情况设置为空数组
UPDATE orders
SET quotation_ids = '[]'
WHERE quotation_id IS NULL OR quotation_id = 0;

-- 7. 为现有的订单产品设置来源信息（有关联报价单的）
UPDATE order_products
SET
    source_type = 'quotation',
    source_id = (
        SELECT CAST(REPLACE(REPLACE(o.quotation_ids, '[', ''), ']', '') AS INTEGER)
        FROM orders o
        WHERE o.id = order_products.order_id
        AND o.quotation_ids != '[]'
    ),
    source_display = (
        SELECT q.quotation_number
        FROM orders o
        JOIN quotations q ON q.id = CAST(REPLACE(REPLACE(o.quotation_ids, '[', ''), ']', '') AS INTEGER)
        WHERE o.id = order_products.order_id
        AND o.quotation_ids != '[]'
    )
WHERE order_id IN (
    SELECT id FROM orders WHERE quotation_ids != '[]' AND quotation_ids IS NOT NULL
);

-- 8. 对于没有关联报价单的产品，保持为手动添加（默认值）
-- 这些产品的 source_type 已经是 'manual'，无需额外操作

-- 9. 删除旧的 quotation_id 字段（在新字段迁移完成后）
-- 注意：SQLite 不支持直接删除列，如果需要删除需要重建表
-- 这里我们保留旧字段作为备份，可以手动删除

-- 10. 验证数据迁移结果
SELECT 'Orders quotation_ids migration check:' as check_type;
SELECT
    id,
    quotation_id as old_quotation_id,
    quotation_ids as new_quotation_ids
FROM orders
LIMIT 5;

SELECT 'Order products source info check:' as check_type;
SELECT
    id,
    order_id,
    source_type,
    source_id,
    source_display
FROM order_products
LIMIT 5;

SELECT 'Source type distribution:' as check_type;
SELECT
    source_type,
    COUNT(*) as count
FROM order_products
GROUP BY source_type;

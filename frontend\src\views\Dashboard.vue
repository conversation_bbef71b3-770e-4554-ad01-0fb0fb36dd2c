<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <template #header>
          <div class="card-header">
            <span>欢迎使用EMB物资管理系统</span>
            <el-dropdown>
              <span class="el-dropdown-link">
                更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>修改密码</el-dropdown-item>
                  <el-dropdown-item>查看帮助</el-dropdown-item>
                  <el-dropdown-item>关于系统</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <div class="welcome-content">
          <div class="welcome-left">
            <div class="avatar-container">
              <el-avatar :size="80" src="https://placehold.co/80x80/4A6FE3/FFFFFF.svg?text=EMB" />
            </div>
            <div class="user-info">
              <h3>{{ userInfo.name }}，{{ greeting }}</h3>
              <p>上次登录：{{ userInfo.lastLoginTime }}</p>
              <p>角色：{{ userInfo.role }}</p>
            </div>
          </div>
          <div class="welcome-right">
            <div class="weather-info">
              <el-icon><Sunny /></el-icon>
              <span>{{ weather.city }} {{ weather.temperature }}℃ {{ weather.condition }}</span>
            </div>
            <div class="date-info">
              <p>{{ currentDate }}</p>
              <p>{{ currentDay }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stat-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.title">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-header">
              <span class="stat-title">{{ stat.title }}</span>
              <el-icon class="stat-icon" :color="stat.color">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-body">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-compare">
                <span>较上周</span>
                <span :class="stat.change > 0 ? 'up' : 'down'">
                  {{ stat.change > 0 ? '+' : '' }}{{ stat.change }}%
                  <el-icon v-if="stat.change > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </span>
              </div>
            </div>
            <div class="stat-footer">
              <el-link type="primary" underline="never" @click="handleStatClick(stat)">{{ stat.linkText }}</el-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和列表 -->
    <div class="charts-tables">
      <el-row :gutter="20">
        <!-- 销售趋势图 -->
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>销售趋势</span>
                <div class="header-right">
                  <el-radio-group v-model="selectedPeriod" size="small" @change="updateChart">
                    <el-radio-button value="week">本周</el-radio-button>
                    <el-radio-button value="month">本月</el-radio-button>
                    <el-radio-button value="quarter">本季度</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            <div ref="chartRef" class="chart-container" v-loading="loadingChart"></div>
          </el-card>
        </el-col>

        <!-- 产品分类占比 -->
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>产品分类数量占比</span>
                <div class="header-right">
                  <el-select v-model="productChartPeriod" size="small" style="width: 120px" @change="updateProductChart">
                    <el-option label="本月" value="month" />
                    <el-option label="本季度" value="quarter" />
                    <el-option label="本年度" value="year" />
                  </el-select>
                </div>
              </div>
            </template>
            <div ref="productChartRef" class="chart-container" v-loading="loadingProductChart"></div>
          </el-card>
        </el-col>

        <!-- 待处理报价单 -->
        <el-col :xs="24" :md="12">
          <el-card class="list-card">
            <template #header>
              <div class="card-header">
                <span>待处理报价单</span>
                <el-link type="primary" underline="never" @click="$router.push('/quotations')">查看全部</el-link>
              </div>
            </template>
            <el-table :data="pendingQuotations" style="width: 100%" :max-height="300" v-loading="loadingQuotations">
              <el-table-column prop="quotation_number" label="报价单号" width="150" />
              <el-table-column prop="customer_name" label="客户名称" min-width="120" />
              <el-table-column prop="total_amount" label="金额" width="120">
                <template #default="scope">
                  ¥{{ scope.row.total_amount.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="160" />
              <el-table-column fixed="right" label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" link @click="handleViewQuotation(scope.row)">查看</el-button>
                  <el-button type="success" link @click="handleApproveQuotation(scope.row)">审批</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 待发货订单 -->
        <el-col :xs="24" :md="12">
          <el-card class="list-card">
            <template #header>
              <div class="card-header">
                <span>待发货订单</span>
                <el-link type="primary" underline="never" @click="$router.push('/orders')">查看全部</el-link>
              </div>
            </template>
            <el-table :data="pendingOrders" style="width: 100%" :max-height="300" v-loading="loadingOrders">
              <el-table-column prop="order_number" label="订单编号" width="150" />
              <el-table-column prop="customer_name" label="客户名称" min-width="120" />
              <el-table-column prop="total_amount" label="金额" width="120">
                <template #default="scope">
                  ¥{{ scope.row.total_amount.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="120" />
              <el-table-column fixed="right" label="操作" width="120">
                <template #default="scope">
                  <el-button type="primary" link @click="handleViewOrder(scope.row)">查看</el-button>
                  <el-button type="success" link @click="handleDeliverOrder(scope.row)">发货</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>快捷操作</span>
          </div>
        </template>
        <div class="actions-container">
          <el-row :gutter="20">
            <el-col :xs="12" :sm="8" :md="4" v-for="action in quickActions" :key="action.name">
              <div class="action-item" @click="handleQuickAction(action)">
                <el-icon :size="32"><component :is="action.icon" /></el-icon>
                <span class="action-name">{{ action.name }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Sunny,
  Document,
  ShoppingCart,
  Money,
  Wallet,
  ArrowUp,
  ArrowDown,
  User,
  Setting,
  Goods,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import {
  getDashboardStats,
  getSalesTrends,
  getProductDistribution,
  getPendingQuotations,
  getPendingOrders,
  getWeatherInfo,
  getUserInfo,
} from '@/api/dashboard'

const router = useRouter()

// 图表引用
const chartRef = ref<HTMLDivElement>()
const productChartRef = ref<HTMLDivElement>()
let chart: echarts.ECharts | null = null
let productChart: echarts.ECharts | null = null

// 加载状态
const loadingChart = ref(false)
const loadingProductChart = ref(false)
const loadingQuotations = ref(false)
const loadingOrders = ref(false)

// 选中的时间周期
const selectedPeriod = ref<'week' | 'month' | 'quarter'>('month')
const productChartPeriod = ref<'month' | 'quarter' | 'year'>('month')

// 用户信息
const userInfo = reactive({
  name: 'Admin',
  lastLoginTime: '2024-12-22 10:00:00',
  role: '管理员',
})

// 天气信息
const weather = reactive({
  city: '深圳',
  temperature: 28,
  condition: '晴',
})

// 统计数据
const stats = ref([
  {
    title: '未处理报价单',
    value: 0,
    change: 0,
    icon: Document,
    color: '#409EFF',
    linkText: '查看报价单',
    route: '/quotations',
  },
  {
    title: '未完成订单',
    value: 0,
    change: 0,
    icon: ShoppingCart,
    color: '#67C23A',
    linkText: '查看订单',
    route: '/orders',
  },
  {
    title: '本月销售额',
    value: '¥0',
    change: 0,
    icon: Money,
    color: '#E6A23C',
    linkText: '查看财务报表',
    route: '/finance/reports',
  },
  {
    title: '待收款项',
    value: '¥0',
    change: 0,
    icon: Wallet,
    color: '#F56C6C',
    linkText: '查看应收款',
    route: '/receivables',
  },
])

// 待处理报价单
const pendingQuotations = ref([])

// 待发货订单
const pendingOrders = ref([])

// 计算属性
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 12) return '上午好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

const currentDate = computed(() => {
  const date = new Date()
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
})

const currentDay = computed(() => {
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return days[new Date().getDay()]
})

// 快捷操作
const quickActions = ref([
  { name: '新增报价单', icon: Document, path: '/quotation-requests/new' },
  { name: '新增客户', icon: User, path: '/customers/new' },
  { name: '产品列表', icon: Goods, path: '/products' },
  { name: '财务报表', icon: Money, path: '/finance/reports' },
  { name: '系统设置', icon: Setting, path: '/settings' },
])

// 事件处理方法
const handleStatClick = (stat: any) => {
  router.push(stat.route)
}

const handleQuickAction = (action: any) => {
  if (action.path) {
    router.push(action.path)
  }
}

const handleViewQuotation = (row: any) => {
  router.push(`/quotations/view/${row.id}`)
}

const handleApproveQuotation = (row: any) => {
  console.log('Approve quotation', row.id)
}

const handleViewOrder = (row: any) => {
  router.push(`/orders/view/${row.id}`)
}

const handleDeliverOrder = (row: any) => {
  console.log('Deliver order', row.id)
}

const updateChart = () => {
  loadSalesTrends()
}

const updateProductChart = () => {
  loadProductDistribution()
}

// 加载统计数据
const loadDashboardStats = async () => {
  try {
    const data = await getDashboardStats() as any
    // 更新统计卡片数据
    stats.value[0].value = data.quotations_total || 0
    stats.value[1].value = data.orders_pending || 0
    stats.value[2].value = `¥${(data.revenue_month || 0).toLocaleString()}`
    stats.value[3].value = `¥${((data.revenue_total || 0) - (data.revenue_month || 0)).toLocaleString()}`

    // 模拟变化百分比（实际项目中应该从API获取）
    stats.value[0].change = Math.floor(Math.random() * 20) - 10
    stats.value[1].change = Math.floor(Math.random() * 20) - 10
    stats.value[2].change = Math.floor(Math.random() * 20) - 10
    stats.value[3].change = Math.floor(Math.random() * 20) - 10
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用默认数据，不显示错误给用户
  }
}

// 加载待处理报价单
const loadPendingQuotations = async () => {
  try {
    loadingQuotations.value = true
    const response = await getPendingQuotations() as any
    // 根据后端API文档，响应格式为 { data: { items: [...], pagination: {...} } }
    pendingQuotations.value = response.data?.items || response.items || response.data || []
  } catch (error) {
    console.error('加载待处理报价单失败:', error)
    pendingQuotations.value = []
  } finally {
    loadingQuotations.value = false
  }
}

// 加载待处理订单
const loadPendingOrders = async () => {
  try {
    loadingOrders.value = true
    const response = await getPendingOrders() as any
    // 根据后端API文档，响应格式为 { data: { items: [...], pagination: {...} } }
    pendingOrders.value = response.data?.items || response.items || response.data || []
  } catch (error) {
    console.error('加载待处理订单失败:', error)
    pendingOrders.value = []
  } finally {
    loadingOrders.value = false
  }
}

// 加载销售趋势数据
const loadSalesTrends = async () => {
  try {
    loadingChart.value = true
    const data = await getSalesTrends(selectedPeriod.value) as any
    if (data && data.length > 0) {
      updateChartWithApiData(data)
    } else {
      // 如果API失败，使用默认数据
      initChart()
    }
  } catch (error) {
    console.error('加载销售趋势失败:', error)
    // 使用默认数据
    initChart()
  } finally {
    loadingChart.value = false
  }
}

// 加载产品分布数据
const loadProductDistribution = async () => {
  try {
    loadingProductChart.value = true
    const data = await getProductDistribution(productChartPeriod.value) as any
    if (data && data.length > 0) {
      updateProductChartWithApiData(data)
    } else {
      // 如果API失败，使用默认数据
      initProductChart()
    }
  } catch (error) {
    console.error('加载产品分布失败:', error)
    // 使用默认数据
    initProductChart()
  } finally {
    loadingProductChart.value = false
  }
}

// 初始化销售趋势图表
const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  const option = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['销售额', '订单数'] },
    xAxis: {
      type: 'category',
      data: selectedPeriod.value === 'week'
        ? ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        : selectedPeriod.value === 'month'
        ? ['第1周', '第2周', '第3周', '第4周']
        : ['第1季度', '第2季度', '第3季度', '第4季度'],
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '销售额',
        type: 'line',
        data: selectedPeriod.value === 'week'
          ? [12, 15, 18, 22, 25, 28, 30]
          : selectedPeriod.value === 'month'
          ? [120, 150, 180, 220]
          : [1200, 1500, 1800, 2200],
        smooth: true,
      },
      {
        name: '订单数',
        type: 'line',
        data: selectedPeriod.value === 'week'
          ? [5, 8, 12, 15, 18, 20, 22]
          : selectedPeriod.value === 'month'
          ? [50, 80, 120, 150]
          : [500, 800, 1200, 1500],
        smooth: true,
      },
    ],
  }

  chart.setOption(option)
}

// 初始化产品分类图表
const initProductChart = () => {
  if (!productChartRef.value) return

  productChart = echarts.init(productChartRef.value)

  const option = {
    tooltip: { trigger: 'item' },
    legend: { top: 'bottom' },
    series: [
      {
        name: '产品分类',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: { show: false, position: 'center' },
        emphasis: {
          label: { show: true, fontSize: '20', fontWeight: 'bold' },
        },
        labelLine: { show: false },
        data: [
          { value: 1048, name: '电子设备' },
          { value: 735, name: '机械配件' },
          { value: 580, name: '化工原料' },
          { value: 484, name: '建筑材料' },
          { value: 300, name: '其他' },
        ],
      },
    ],
  }

  productChart.setOption(option)
}

// 使用API数据更新销售趋势图表
const updateChartWithApiData = (apiData: any[]) => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  // 处理API数据格式
  const labels = apiData.map(item => item.date || item.label)
  const salesData = apiData.map(item => item.amount || item.sales || 0)
  const ordersData = apiData.map(item => item.orders_count || item.orders || 0)

  const option = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['销售额', '订单数'] },
    xAxis: {
      type: 'category',
      data: labels,
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '销售额',
        type: 'line',
        data: salesData,
        smooth: true,
      },
      {
        name: '订单数',
        type: 'line',
        data: ordersData,
        smooth: true,
      },
    ],
  }

  chart.setOption(option)
}

// 使用API数据更新产品分类图表
const updateProductChartWithApiData = (apiData: any[]) => {
  if (!productChartRef.value) return

  productChart = echarts.init(productChartRef.value)

  // 处理API数据格式
  const chartData = apiData.map(item => ({
    value: item.sales_amount || item.total_quantity || item.value || 0,
    name: item.category_name || item.name || '未知分类'
  }))

  const option = {
    tooltip: { trigger: 'item' },
    legend: { top: 'bottom' },
    series: [
      {
        name: '产品分类',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: { show: false, position: 'center' },
        emphasis: {
          label: { show: true, fontSize: '20', fontWeight: 'bold' },
        },
        labelLine: { show: false },
        data: chartData,
      },
    ],
  }

  productChart.setOption(option)
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const response = await getUserInfo() as any
    if (response.data) {
      userInfo.name = response.data.username || 'Admin'
      userInfo.role = response.data.role || '管理员'
      userInfo.lastLoginTime = response.data.last_login || new Date().toLocaleString()
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 使用默认数据
  }
}

// 加载天气信息
const loadWeatherInfo = async () => {
  try {
    const response = await getWeatherInfo('深圳') as any
    if (response.data) {
      weather.city = response.data.city || '深圳'
      weather.temperature = response.data.temperature || 28
      weather.condition = response.data.condition || '晴'
    }
  } catch (error) {
    console.error('加载天气信息失败:', error)
    // 使用默认数据
  }
}

onMounted(async () => {
  await nextTick()

  // 加载所有数据
  loadDashboardStats()
  loadSalesTrends()
  loadProductDistribution()
  loadPendingQuotations()
  loadPendingOrders()
  loadUserInfo()
  loadWeatherInfo()

  // 监听窗口大小变化
  const handleResize = () => {
    chart?.resize()
    productChart?.resize()
  }

  window.addEventListener('resize', handleResize)

  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    chart?.dispose()
    productChart?.dispose()
  })
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .welcome-card {
    margin-bottom: 20px;

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }

      .welcome-left {
        display: flex;
        align-items: center;

        .avatar-container {
          margin-right: 20px;
        }

        .user-info {
          h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 18px;
            color: #303133;
          }

          p {
            margin: 5px 0;
            color: #606266;
          }
        }
      }

      .welcome-right {
        text-align: right;

        @media (max-width: 768px) {
          text-align: left;
          margin-top: 15px;
        }

        .weather-info {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-bottom: 10px;

          @media (max-width: 768px) {
            justify-content: flex-start;
          }

          .el-icon {
            margin-right: 8px;
            font-size: 20px;
            color: #E6A23C;
          }
        }

        .date-info {
          p {
            margin: 5px 0;
            color: #606266;
          }
        }
      }
    }
  }

  .stat-cards {
    margin-bottom: 20px;

    .stat-card {
      height: 100%;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
      }

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .stat-title {
          font-size: 16px;
          color: #606266;
        }

        .stat-icon {
          font-size: 26px;
        }
      }

      .stat-body {
        .stat-value {
          font-size: 26px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
        }

        .stat-compare {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #909399;

          .up {
            color: #67C23A;
            display: flex;
            align-items: center;
          }

          .down {
            color: #F56C6C;
            display: flex;
            align-items: center;
          }
        }
      }

      .stat-footer {
        margin-top: 20px;

        .el-link {
          font-size: 14px;
        }
      }
    }
  }


  .charts-tables {
    margin-bottom: 20px;

    .chart-card, .list-card {
      margin-bottom: 20px;

      .header-right {
        display: flex;
        align-items: center;
      }

      .chart-container {
        height: 300px;
      }
    }
  }

  .quick-actions {
    margin-bottom: 20px;

    .actions-container {
      padding: 10px 0;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px;
        text-align: center;
        border-radius: 4px;
        transition: all 0.3s;
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
          color: #409EFF;
        }

        .el-icon {
          margin-bottom: 10px;
        }

        .action-name {
          font-size: 14px;
        }
      }
    }
  }
}
</style>

#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的数据库表和初始数据
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from datetime import datetime
from decimal import Decimal

def init_database():
    """初始化数据库"""
    app = create_app()
    
    with app.app_context():
        print("🗄️  正在创建数据库表...")
        
        # 删除所有表（如果存在）
        db.drop_all()
        print("   ✅ 已清理旧表")
        
        # 创建所有表
        db.create_all()
        print("   ✅ 已创建所有表")
        
        # 创建初始数据
        create_initial_data()
        
        print("🎉 数据库初始化完成！")

def create_initial_data():
    """创建初始数据"""
    print("📊 正在创建初始数据...")
    
    # 1. 创建产品分类
    categories = [
        ProductCategory(name='电子设备', description='各类电子设备和器件'),
        ProductCategory(name='机械配件', description='机械零部件和配件'),
        ProductCategory(name='化工原料', description='化工材料和原料'),
        ProductCategory(name='建筑材料', description='建筑用材料和设备'),
        ProductCategory(name='办公用品', description='办公设备和用品'),
    ]
    
    for category in categories:
        db.session.add(category)
    
    db.session.commit()
    print("   ✅ 已创建产品分类")
    
    # 2. 创建品牌
    brands = [
        Brand(name='华为', description='华为技术有限公司'),
        Brand(name='小米', description='小米科技有限公司'),
        Brand(name='苹果', description='苹果公司'),
        Brand(name='三星', description='三星电子'),
        Brand(name='联想', description='联想集团'),
    ]
    
    for brand in brands:
        db.session.add(brand)
    
    db.session.commit()
    print("   ✅ 已创建品牌数据")
    
    # 3. 创建客户
    customers = [
        Customer(
            name='华为技术有限公司',
            contact='张经理',
            phone='13800138001',
            email='<EMAIL>',
            address='深圳市龙岗区华为基地',
            tax_id='91440300708461136T',
            source='网络推广',
            level='A',
            status='正常'
        ),
        Customer(
            name='小米科技有限公司',
            contact='李经理',
            phone='13800138002',
            email='<EMAIL>',
            address='北京市海淀区小米科技园',
            tax_id='91110108MA001234X',
            source='老客户推荐',
            level='A',
            status='正常'
        ),
        Customer(
            name='苹果公司',
            contact='王经理',
            phone='13800138003',
            email='<EMAIL>',
            address='上海市浦东新区苹果大厦',
            tax_id='91310115MA1234567',
            source='展会',
            level='S',
            status='正常'
        ),
    ]
    
    for customer in customers:
        db.session.add(customer)
    
    db.session.commit()
    print("   ✅ 已创建客户数据")
    
    # 4. 创建产品
    products = [
        Product(
            name='iPhone 15 Pro',
            model='A3102',
            category_id=1,  # 电子设备
            brand_id=3,     # 苹果
            unit='台',
            status='正常',
            description='苹果最新旗舰手机'
        ),
        Product(
            name='华为Mate 60 Pro',
            model='ALN-AL00',
            category_id=1,  # 电子设备
            brand_id=1,     # 华为
            unit='台',
            status='正常',
            description='华为旗舰手机'
        ),
        Product(
            name='小米14 Ultra',
            model='2405CPX3DC',
            category_id=1,  # 电子设备
            brand_id=2,     # 小米
            unit='台',
            status='正常',
            description='小米影像旗舰'
        ),
    ]

    for product in products:
        db.session.add(product)

    db.session.commit()
    print("   ✅ 已创建产品数据")

    # 5. 创建产品规格和价格
    specifications = [
        ProductSpecification(
            product_id=1,  # iPhone 15 Pro
            specification='128GB 深空黑色',
            cost_price=Decimal('6000.00'),
            suggested_price=Decimal('8999.00'),
            min_price=Decimal('8500.00'),
            max_price=Decimal('9500.00'),
            tax_rate=13.0,
            is_default=True
        ),
        ProductSpecification(
            product_id=2,  # 华为Mate 60 Pro
            specification='256GB 雅川青',
            cost_price=Decimal('4500.00'),
            suggested_price=Decimal('6999.00'),
            min_price=Decimal('6500.00'),
            max_price=Decimal('7500.00'),
            tax_rate=13.0,
            is_default=True
        ),
        ProductSpecification(
            product_id=3,  # 小米14 Ultra
            specification='512GB 钛金属',
            cost_price=Decimal('4000.00'),
            suggested_price=Decimal('5999.00'),
            min_price=Decimal('5500.00'),
            max_price=Decimal('6500.00'),
            tax_rate=13.0,
            is_default=True
        ),
    ]

    for spec in specifications:
        db.session.add(spec)

    db.session.commit()
    print("   ✅ 已创建产品规格数据")
    
    # 6. 创建系统设置
    settings = [
        SystemSetting(key='company_name', value='EMB工程物资管理系统', description='公司名称'),
        SystemSetting(key='company_address', value='深圳市南山区科技园', description='公司地址'),
        SystemSetting(key='company_phone', value='0755-12345678', description='公司电话'),
        SystemSetting(key='company_email', value='<EMAIL>', description='公司邮箱'),
    ]
    
    for setting in settings:
        db.session.add(setting)
    
    db.session.commit()
    print("   ✅ 已创建系统设置")
    
    print("📊 初始数据创建完成！")

if __name__ == '__main__':
    init_database()

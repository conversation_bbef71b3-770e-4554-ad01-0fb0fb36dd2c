<template>
  <div class="quotation-request-edit">
    <el-card class="header-card mb-20">
      <div class="flex-between">
        <h2 class="page-title">{{ isEdit ? '编辑报价需求' : '新增报价需求' }}</h2>
        <div>
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            保存
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="quotation-request-form"
      >
        <!-- 基本信息 -->
        <el-form-item label="客户" prop="customer_id">
          <div style="display: flex; width: 70%; align-items: flex-start;">
            <el-select
              v-model="form.customer_id"
              filterable
              remote
              :placeholder="customerNameFromImport && form.customer_id === undefined ? `新客户 (待创建): ${customerNameFromImport}` : '请选择或搜索客户'"
              :remote-method="handleSearchCustomers"
              :loading="customerLoading"
              clearable
              style="flex-grow: 1; margin-right: 10px;"
              @clear="() => { form.customer_id = undefined; customerNameFromImport = '' }"
              @change="(selectedId: number | undefined) => { if (typeof selectedId === 'number') customerNameFromImport = '' }"
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button
              v-if="customerNameFromImport && form.customer_id === undefined"
              type="success"
              @click="handleQuickCreateCustomer"
              title="根据导入的客户名称快速创建新客户"
            >
              创建: {{ customerNameFromImport }}
            </el-button>
          </div>
          <div v-if="customerNameFromImport && form.customer_id === undefined" class="el-form-item__extra_info">
            当前客户 "{{ customerNameFromImport }}" 来自导入，数据库中可能不存在。您可以直接创建或选择其他已有客户。
          </div>
        </el-form-item>

        <el-form-item label="项目名称" prop="project_name">
          <el-input v-model="form.project_name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目地址">
          <el-input
            v-model="form.project_address"
            type="textarea"
            :rows="2"
            placeholder="请输入项目详细地址"
          />
        </el-form-item>

        <el-form-item label="预计采购时间" prop="expected_date">
          <el-date-picker
            v-model="form.expected_date"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <!-- 当前状态 -->
        <el-form-item label="当前状态">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-tag
              :type="form.status === '已确认' ? 'success' : 'warning'"
              size="large"
            >
              {{ form.status || '待确认' }}
            </el-tag>

            <!-- 待确认状态显示确认按钮 -->
            <el-button
              v-if="form.status !== '已确认' && isEdit"
              type="primary"
              size="small"
              :loading="confirmStatusLoading"
              @click="handleConfirmStatus"
            >
              确认
            </el-button>

            <!-- 已确认状态显示创建报价单和转为未确认按钮 -->
            <template v-if="form.status === '已确认' && isEdit">
              <el-button
                type="success"
                size="small"
                @click="handleGenerateQuotation"
              >
                创建报价单
              </el-button>
              <el-button
                type="warning"
                size="small"
                :loading="unconfirmStatusLoading"
                @click="handleUnconfirmStatus"
              >
                转为未确认
              </el-button>
            </template>

            <!-- 导出需求表按钮 - 无论状态如何都显示 -->
            <el-button
              v-if="isEdit"
              type="info"
              size="small"
              :loading="exportLoading"
              @click="handleExportRequest"
            >
              <el-icon><Download /></el-icon>
              导出需求表
            </el-button>
          </div>
        </el-form-item>

      <!-- 产品明细 -->
        <el-divider content-position="left">
          <span>产品明细</span>
          <span v-if="isConfirmed" style="color: #909399; font-size: 12px; margin-left: 10px;">
            （编辑产品明细需要转为未确认状态）
          </span>
        </el-divider>

        <div class="table-operations mb-20">
          <div class="operations-left">
            <el-button
              type="success"
              :disabled="isConfirmed"
              @click="handleOpenBatchAddDialog"
              style="margin-right: 10px;"
            >
              <el-icon><Finished /></el-icon>
              批量添加
            </el-button>
            <el-button
              type="warning"
              :disabled="isConfirmed"
              @click="importDialogVisible = true"
              style="margin-right: 10px;"
            >
              <el-icon><DocumentAdd /></el-icon>
              导入Excel
            </el-button>
            <el-button
              type="info"
              :disabled="isConfirmed"
              @click="handleDownloadTemplate"
              style="margin-right: 10px;"
            >
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
            <el-button
              type="primary"
              :disabled="isConfirmed"
              @click="handleAddItem"
              style="margin-right: 10px;"
            >
              <el-icon><Plus /></el-icon>
              添加产品
            </el-button>
            <el-button
              type="warning"
              :disabled="isConfirmed"
              :loading="autoStandardizeLoading"
              @click="handleAutoStandardizeItems"
              style="margin-right: 10px;"
            >
              <el-icon><Refresh /></el-icon>
              自动匹配(待匹配条目)
            </el-button>
            <el-button
              type="info"
              :disabled="isConfirmed"
              @click="openStandardizeDialog"
            >
              <el-icon><Finished /></el-icon>
              手动匹配
            </el-button>
          </div>


        </div>

        <!-- 产品明细表格 -->
        <el-table :data="form.items" border style="width: 100%">
          <el-table-column type="index" width="50" />
          <el-table-column label="产品名称" min-width="200">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_name"
                  placeholder="请输入产品名称"
                  :disabled="isConfirmed"
                  @input="handleProductNameInput(row)"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductName(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="产品型号" min-width="150">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_model"
                  placeholder="请输入产品型号"
                  :disabled="isConfirmed"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductModel(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="规格" min-width="150">
            <template #default="{ row }">
              <div>
                <el-input
                  v-model="row.product_spec"
                  placeholder="请输入规格"
                  :disabled="isConfirmed"
                  style="width: 100%;"
                />
                <div v-if="row.matched_product_specification_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedSpecificationName(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="140">
            <template #default="{ row }">
              <el-input-number
                v-model="row.quantity"
                :min="1"
                :precision="0"
                :disabled="isConfirmed"
                class="quantity-input"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="单位" width="100">
            <template #default="{ row }">
              <div>
                <el-input v-model="row.unit" placeholder="单位" :disabled="isConfirmed" style="width: 100%;" />
                <div v-if="row.matched_product_id" class="matched-info">
                  <el-icon><Link /></el-icon>
                  <span>{{ getMatchedProductUnit(row) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="匹配状态" width="140">
            <template #default="{ row }">
              <div>
                <div v-if="row.matched_product_id">
                  <el-tag
                    :type="row.matched_product_specification_id ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ row.matched_product_specification_id ? '已匹配' : '部分匹配' }}
                  </el-tag>
                  <el-tag
                    v-if="row.match_type === 'auto'"
                    type="info"
                    size="small"
                    style="margin-top: 2px;"
                  >
                    自动
                  </el-tag>
                  <el-tag
                    v-else-if="row.match_type === 'manual'"
                    type="primary"
                    size="small"
                    style="margin-top: 2px;"
                  >
                    手动
                  </el-tag>
                </div>
                <el-tag v-else type="warning" size="small">
                  待匹配
                </el-tag>
                <div style="margin-top: 5px;">
                  <el-button
                    :type="row.matched_product_id ? '' : 'primary'"
                    :class="{ 'light-blue-btn': row.matched_product_id }"
                    :disabled="isConfirmed"
                    size="small"
                    @click="openSingleStandardizeDialog(row)"
                  >
                    调整匹配
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.notes"
                type="textarea"
                placeholder="请输入备注"
                :disabled="isConfirmed"
                :rows="3"
                resize="vertical"
                style="width: 100%;"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button
                type="danger"
                size="small"
                :disabled="isConfirmed"
                @click="handleRemoveItem($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>


      </el-form>
      </el-card>

    <!-- 批量添加产品对话框 -->
    <el-dialog
      v-model="batchAddDialogVisible"
      title="批量添加产品"
      width="60%"
      @close="handleBatchDialogClose"
    >
      <el-alert
        title="请从Excel中复制数据（包括表头则会自动忽略第一行），按以下列顺序粘贴，并确保每列数据间使用制表符(Tab)分隔："
        type="info"
        show-icon
        :closable="false"
        class="mb-20"
      >
        <p>列顺序：产品名称 | 产品型号 | 规格 | 数量 | 单位 | 备注</p>
        <p>示例：</p>
        <pre>
产品A  型号X  大号  10  个  这是备注A
产品B  型号Y  中号  5   件  这是备注B
        </pre>
      </el-alert>
      <el-input
        v-model="batchAddText"
        type="textarea"
        :rows="10"
        placeholder="请在此处粘贴Excel数据..."
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchAddDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmBatchAdd">
            确认添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入Excel对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入产品明细" width="500px" @close="resetImportForm">
      <el-form ref="importFormRef" label-width="100px">
        <el-form-item label="Excel文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :show-file-list="true"
            :limit="1"
            :on-exceed="handleUploadExceed"
            :on-change="handleFileChange"
            :auto-upload="false"
            accept=".xlsx,.xls"
          >
            <el-icon class="el-icon--upload"><DocumentAdd /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .xlsx/.xls 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="导入说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>• 请使用标准模板格式的Excel文件</p>
            <p>• 导入的产品将添加到当前产品列表中</p>
            <p>• 如果产品名称重复，将会追加新的条目</p>
          </el-alert>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport" :loading="importLoading">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 标准化处理/手动匹配调整 对话框 -->
    <el-dialog
      v-model="standardizeDialogVisible"
      title="需求条目匹配调整"
      width="95%"
      destroy-on-close
      top="3vh"
    >
      <el-table
        :data="standardizeItems"
        border
        stripe
        max-height="calc(90vh - 150px)"
      >
        <el-table-column type="index" label="#" width="45" fixed />
        <el-table-column prop="product_name" label="原始名称" min-width="150" show-overflow-tooltip fixed>
          <template #default="{row}">{{ row.product_name }}</template>
        </el-table-column>
        <el-table-column prop="product_model" label="原始型号" min-width="120" show-overflow-tooltip>
           <template #default="{row}">{{ row.product_model || '---' }}</template>
        </el-table-column>
        <el-table-column prop="product_spec" label="原始规格" min-width="150" show-overflow-tooltip>
          <template #default="{row}">{{ row.product_spec || '---' }}</template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />

        <el-table-column label="匹配产品" min-width="200">
          <template #default="{ row }">
            <el-select
              v-model="row.form_matched_product_id"
              filterable
              clearable
              placeholder="选择或搜索系统产品"
              style="width: 100%"
              @change="(productId) => handleDialogProductChange(row, productId)"
              remote
              :remote-method="searchProductsForDialog"
            >
              <el-option
                v-for="product in productOptionsForDialog"
                :key="product.id"
                :label="`${product.name}${product.model ? ` (${product.model})` : ''}`"
                :value="product.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="匹配规格" min-width="200">
          <template #default="{ row }">
            <el-select
              v-model="row.form_matched_specification_id"
              filterable
              clearable
              placeholder="选择产品规格"
              style="width: 100%"
              :disabled="!row.form_matched_product_id"
            >
              <el-option
                v-for="spec in getSpecificationsForProduct(row.form_matched_product_id)"
                :key="spec.id"
                :label="`${spec.specification} - ¥${spec.suggested_price || '0.00'}`"
                :value="spec.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="当前匹配状态" min-width="120">
          <template #default="{ row }">
            <el-tag v-if="row.form_matched_specification_id" type="success" size="small">
              已匹配
            </el-tag>
            <el-tag v-else-if="row.form_matched_product_id && !row.form_matched_specification_id" type="danger" size="small">
              需选择规格
            </el-tag>
            <el-tag v-else type="warning" size="small">
              待匹配
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="standardizeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStandardize" :loading="isSubmittingMatch">确认更新匹配</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Document,
  Check,
  Plus,
  Finished,
  DocumentAdd,
  Download,
  Connection,
  Link
} from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { quotationRequestApi } from '@/api/quotation'
import { customerApi, searchCustomers } from '@/api/customer'
import { productApi } from '@/api/product'
import type { UploadInstance, UploadProps, UploadRawFile, UploadFile, FormInstance } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const loading = ref(false)
const submitting = ref(false)
const customerLoading = ref(false)

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)
// 是否已确认状态
const isConfirmed = computed(() => form.status === '已确认')

// 选项数据
const customerOptions = ref<Array<any>>([])

// 批量添加对话框
const batchAddDialogVisible = ref(false)
const batchAddText = ref('')

// 导入相关
const importDialogVisible = ref(false)
const importLoading = ref(false)
const uploadRef = ref<UploadInstance>()
const importFormRef = ref<FormInstance>()
const fileToUpload = ref<UploadFile | null>(null)

// 客户创建相关
const customerNameFromImport = ref('')

// 匹配相关
const autoStandardizeLoading = ref(false)
const standardizeDialogVisible = ref(false)
const standardizeItems = ref<any[]>([])
const productOptionsForDialog = ref<any[]>([])
const productSpecificationsMap = ref<any>({})

const confirmStatusLoading = ref(false)
const unconfirmStatusLoading = ref(false)
const exportLoading = ref(false)
const isSubmittingMatch = ref(false)


// 表单数据
const form = reactive({
  customer_id: '',
  project_name: '',
  project_address: '',
  expected_date: '',
  notes: '',
  status: '待确认',
  items: [] as Array<{
    product_name: string
    product_model: string
    product_spec: string
    quantity: number
    unit: string
    notes: string
    matched_product_id?: number
    matched_product_specification_id?: number
    matched_specification?: any
    matched_product?: any
    [key: string]: any
  }>
})

// 表单验证规则
const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  project_address: [
    { max: 200, message: '项目地址长度不能超过 200 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 1000, message: '备注长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 搜索客户
const handleSearchCustomers = async (query: string) => {
  if (query) {
    customerLoading.value = true
    try {
      const response = await searchCustomers({ name: query, per_page: 20 }) as any
      customerOptions.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('搜索客户失败:', error)
    } finally {
      customerLoading.value = false
    }
  } else {
    customerOptions.value = []
  }
}

// 快速创建客户
const handleQuickCreateCustomer = () => {
  // TODO: 实现快速创建客户功能
  ElMessage.info('快速创建客户功能开发中...')
}

// 获取报价需求详情（编辑模式）
const getRequestDetail = async () => {
  if (!isEdit.value) return

  try {
    loading.value = true
    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.getById(requestId) as any
    const request = response.data || response

    // 填充表单数据
    Object.assign(form, {
      customer_id: request.customer_id || '',
      project_name: request.project_name || '',
      project_address: request.project_address || '',
      expected_date: request.expected_date || '',
      notes: request.notes || '',
      status: request.status || '待确认',
      items: request.items || []
    })

    // 如果有客户ID，加载客户信息
    if (request.customer_id && request.customer) {
      customerOptions.value = [request.customer]
    }
  } catch (error) {
    console.error('获取报价需求详情失败:', error)
    ElMessage.error('获取报价需求详情失败')
  } finally {
    loading.value = false
  }
}

// 产品项目操作
const handleAddItem = () => {
  form.items.unshift({
    product_name: '',
    product_model: '',
    product_spec: '',
    quantity: 1,
    unit: '',
    notes: ''
  })
}

const handleRemoveItem = (index: number) => {
  form.items.splice(index, 1)
  ElMessage.success('删除成功')
}

// 产品名称输入处理（可以添加自动匹配逻辑）
const handleProductNameInput = (row: any) => {
  // TODO: 可以在这里添加产品名称自动匹配逻辑
  console.log('产品名称输入:', row.product_name)
}

// 批量添加相关
const handleOpenBatchAddDialog = () => {
  batchAddDialogVisible.value = true
  batchAddText.value = ''
}

const handleBatchDialogClose = () => {
  batchAddText.value = ''
}

const handleConfirmBatchAdd = () => {
  if (!batchAddText.value.trim()) {
    ElMessage.warning('请输入要添加的产品数据')
    return
  }

  try {
    const lines = batchAddText.value.trim().split('\n')
    const newItems: any[] = []

    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      if (!trimmedLine) return

      // 如果第一行包含"产品名称"等关键词，跳过（表头）
      if (index === 0 && (trimmedLine.includes('产品名称') || trimmedLine.includes('名称'))) {
        return
      }

      const columns = trimmedLine.split('\t') // 使用制表符分割
      if (columns.length >= 4) { // 至少需要产品名称、型号、规格、数量
        newItems.push({
          product_name: columns[0]?.trim() || '',
          product_model: columns[1]?.trim() || '',
          product_spec: columns[2]?.trim() || '',
          quantity: parseInt(columns[3]?.trim()) || 1,
          unit: columns[4]?.trim() || '',
          notes: columns[5]?.trim() || ''
        })
      }
    })

    // 将新项目添加到表格顶部
    if (newItems.length > 0) {
      form.items.unshift(...newItems)
    }

    if (newItems.length > 0) {
      ElMessage.success(`成功添加 ${newItems.length} 个产品`)
      batchAddDialogVisible.value = false
      batchAddText.value = ''
    } else {
      ElMessage.warning('没有识别到有效的产品数据，请检查格式')
    }
  } catch (error) {
    console.error('批量添加失败:', error)
    ElMessage.error('批量添加失败，请检查数据格式')
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  if (!isEdit.value) {
    Object.assign(form, {
      customer_id: '',
      project_name: '',
      project_address: '',
      expected_date: '',
      notes: '',
      items: []
    })
  }
}

// 返回操作
const handleCancel = async () => {
  // 检查是否有未保存的数据
  const hasUnsavedData = form.customer_id || form.project_name || form.project_address ||
                        form.expected_date || form.notes || form.items.length > 0

  if (hasUnsavedData) {
    try {
      await ElMessageBox.confirm(
        '当前页面有未保存的数据，返回后将会丢失。确定要返回吗？',
        '确认返回',
        {
          confirmButtonText: '确定返回',
          cancelButtonText: '继续编辑',
          type: 'warning',
        }
      )
    } catch {
      // 用户点击取消，不执行返回操作
      return
    }
  }

  // 返回列表页
  router.push('/quotation-requests')
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    // 检查是否有产品明细
    if (!form.items.length) {
      ElMessage.warning('请至少添加一个产品')
      return
    }

    loading.value = true

    // 准备提交数据
    const submitData = { ...form }

    // 清理items中的只读字段，但保留匹配信息
    if (submitData.items && Array.isArray(submitData.items)) {
      submitData.items = submitData.items.map(item => {
        const cleanItem = { ...item }
        // 移除只读字段
        delete cleanItem.id
        delete cleanItem.created_at
        delete cleanItem.updated_at
        // 保留匹配信息，这些需要保存到后端
        // delete cleanItem.matched_product_id
        // delete cleanItem.matched_product_specification_id
        // 移除前端临时字段
        delete cleanItem.matched_product
        delete cleanItem.matched_specification
        delete cleanItem.original_product_name
        delete cleanItem.original_product_model
        delete cleanItem.original_product_spec
        delete cleanItem.original_unit
        // 移除表单临时字段
        delete cleanItem.form_matched_product_id
        delete cleanItem.form_matched_specification_id

        // 确保单位字段不为空
        if (!cleanItem.unit || cleanItem.unit.trim() === '') {
          // 尝试从匹配的产品获取单位
          if (item.matched_product?.unit) {
            cleanItem.unit = item.matched_product.unit
          } else {
            // 提供默认单位
            cleanItem.unit = '个'
          }
        }

        return cleanItem
      })
    }

    // 处理空值
    Object.keys(submitData).forEach(key => {
      if ((submitData as any)[key] === '' || (submitData as any)[key] === null) {
        delete (submitData as any)[key]
      }
    })

    if (isEdit.value) {
      // 更新报价需求
      const requestId = Number(route.params.id)
      await quotationRequestApi.update(requestId, submitData)
      ElMessage.success('更新报价需求成功')

      // 编辑模式下跳转到详情页
      router.push(`/quotation-requests/view/${requestId}`)
    } else {
      // 创建报价需求
      const response = await quotationRequestApi.create(submitData)
      ElMessage.success('创建报价需求成功')

      // 新建模式下跳转到列表页
      router.push('/quotation-requests')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新报价需求失败' : '创建报价需求失败')
  } finally {
    loading.value = false
  }
}

// 导入相关函数
const resetImportForm = () => {
  fileToUpload.value = null
  uploadRef.value?.clearFiles()
  if (importFormRef.value) {
    importFormRef.value.resetFields()
  }
}

const handleFileChange: UploadProps['onChange'] = async (uploadFile) => {
  if (uploadFile.raw) {
    const isExcel = uploadFile.name.endsWith('.xlsx') || uploadFile.name.endsWith('.xls')
    const isLt10M = (uploadFile.size || 0) / 1024 / 1024 < 10

    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件 (.xlsx, .xls)!')
      uploadRef.value?.clearFiles()
      fileToUpload.value = null
      return
    }

    if (!isLt10M) {
      ElMessage.error('上传文件大小不能超过 10MB!')
      uploadRef.value?.clearFiles()
      fileToUpload.value = null
      return
    }

    fileToUpload.value = uploadFile

    // 验证Excel文件格式
    try {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = e.target?.result
          const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
          const firstSheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[firstSheetName]
          const sheetData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

          if (sheetData && sheetData.length > 1) {
            const hasProductData = sheetData.some((row, index) =>
              index > 0 && row.length > 0 && row[0]
            )

            if (hasProductData) {
              ElMessage.success('Excel文件格式正确，包含产品数据')
            } else {
              ElMessage.warning('Excel文件中未检测到产品数据，请确认文件格式正确')
            }
          } else {
            ElMessage.error('Excel文件内容为空或格式不正确')
          }
        } catch (parseError) {
          console.error('解析Excel文件失败:', parseError)
          ElMessage.error('解析Excel文件失败，请检查文件格式')
        }
      }
      reader.readAsBinaryString(uploadFile.raw)
    } catch (error) {
      console.error('读取Excel文件失败:', error)
      ElMessage.error('读取Excel文件失败')
    }
  } else {
    fileToUpload.value = null
  }
}

const handleUploadExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  uploadRef.value!.handleStart(file)

  const newUploadFile: UploadFile = {
    name: file.name,
    size: file.size,
    raw: file,
    uid: file.uid,
    status: 'ready'
  }
  handleFileChange(newUploadFile, [newUploadFile])
}

const submitImport = async () => {
  if (!fileToUpload.value) {
    ElMessage.warning('请先选择要导入的Excel文件')
    return
  }

  importLoading.value = true
  try {
    // 解析Excel文件并添加到产品列表
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const data = e.target?.result
        const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const sheetData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })

        const newItems: any[] = []

        sheetData.forEach((row, index) => {
          // 跳过表头
          if (index === 0) return

          // 检查是否有有效数据
          if (row.length >= 3 && row[0] && row[2] && row[3]) {
            newItems.push({
              product_name: row[0]?.toString().trim() || '',
              product_model: row[1]?.toString().trim() || '',
              product_spec: row[2]?.toString().trim() || '',
              quantity: parseInt(row[3]?.toString()) || 1,
              unit: row[4]?.toString().trim() || '',
              notes: row[5]?.toString().trim() || ''
            })
          }
        })

        // 将新项目添加到表格顶部
        if (newItems.length > 0) {
          form.items.unshift(...newItems)
          ElMessage.success(`成功导入 ${newItems.length} 个产品`)
          importDialogVisible.value = false
          resetImportForm()
        } else {
          ElMessage.warning('没有识别到有效的产品数据，请检查文件格式')
        }
      } catch (parseError) {
        console.error('解析Excel文件失败:', parseError)
        ElMessage.error('解析Excel文件失败，请检查文件格式')
      } finally {
        importLoading.value = false
      }
    }
    reader.readAsBinaryString(fileToUpload.value.raw!)
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
    importLoading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const response = await quotationRequestApi.downloadTemplate()

    const blob = new Blob([response as any], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `询价单模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 单个条目调整匹配
const openSingleStandardizeDialog = async (item: any) => {
  await openStandardizeDialogForItem(item)
}

const handleAutoStandardizeItems = async () => {
  if (!form.items.length) {
    ElMessage.warning('没有需要规整的产品项目')
    return
  }

  // 检查是否有待匹配的条目
  const unmatchedItems = form.items.filter(item => !item.matched_product_id)
  if (unmatchedItems.length === 0) {
    ElMessage.info('所有产品都已匹配，无需进行自动匹配')
    return
  }

  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求，然后再进行自动匹配')
    return
  }

  autoStandardizeLoading.value = true
  try {
    // 先保存当前状态（包括删除操作），但不显示成功消息
    await saveCurrentState()

    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.autoMatch(requestId) as any
    const result = response.data || response

    // 重新加载数据以显示匹配结果
    await getRequestDetail()

    ElMessage.success(`自动匹配完成！处理了 ${unmatchedItems.length} 个待匹配条目，成功匹配 ${result.matched_count || 0} 个产品`)
  } catch (error) {
    console.error('自动匹配失败:', error)
    ElMessage.error('自动匹配失败')
  } finally {
    autoStandardizeLoading.value = false
  }
}

// 内部保存函数，不显示成功消息
const saveCurrentState = async () => {
  // 表单验证
  await formRef.value?.validate()

  // 检查是否有产品明细
  if (!form.items.length) {
    throw new Error('请至少添加一个产品')
  }

  // 准备提交数据
  const submitData = { ...form }

  // 清理items中的只读字段，但保留匹配信息
  if (submitData.items && Array.isArray(submitData.items)) {
    submitData.items = submitData.items.map(item => {
      const cleanItem = { ...item }
      // 移除只读字段
      delete cleanItem.id
      delete cleanItem.created_at
      delete cleanItem.updated_at
      // 移除前端临时字段
      delete cleanItem.matched_product
      delete cleanItem.matched_specification
      delete cleanItem.original_product_name
      delete cleanItem.original_product_model
      delete cleanItem.original_product_spec
      delete cleanItem.original_unit
      delete cleanItem.form_matched_product_id
      delete cleanItem.form_matched_specification_id

      // 确保单位字段不为空
      if (!cleanItem.unit || cleanItem.unit.trim() === '') {
        // 尝试从匹配的产品获取单位
        if (item.matched_product?.unit) {
          cleanItem.unit = item.matched_product.unit
        } else {
          // 提供默认单位
          cleanItem.unit = '个'
        }
      }

      return cleanItem
    })
  }

  // 处理空值
  Object.keys(submitData).forEach(key => {
    if ((submitData as any)[key] === '' || (submitData as any)[key] === null) {
      delete (submitData as any)[key]
    }
  })

  if (isEdit.value) {
    // 更新报价需求
    const requestId = Number(route.params.id)
    await quotationRequestApi.update(requestId, submitData)
  } else {
    // 创建报价需求
    await quotationRequestApi.create(submitData)
  }
}





// 确认状态
const handleConfirmStatus = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  // 检查是否有产品明细
  if (!form.items.length) {
    ElMessage.warning('请先添加产品明细')
    return
  }

  confirmStatusLoading.value = true
  try {
    // 先保存产品明细，确保最新的匹配状态被保存
    await saveCurrentState()

    // 重新获取最新数据，确保检查的是后端保存的状态
    await getRequestDetail()

    // 检查所有产品是否都已匹配
    const unmatchedItems = form.items.filter(item => !item.matched_product_id)
    if (unmatchedItems.length > 0) {
      const unmatchedNames = unmatchedItems.map(item => item.product_name || '未命名产品').join('、')
      ElMessage.warning(`以下产品尚未匹配，请先完成匹配：${unmatchedNames}`)
      return
    }

    // 更新状态为已确认
    const requestId = Number(route.params.id)
    await quotationRequestApi.updateStatus(requestId, { status: '已确认' })

    // 更新本地状态
    form.status = '已确认'

    ElMessage.success('确认成功')
  } catch (error) {
    console.error('确认失败:', error)
    ElMessage.error('确认失败')
  } finally {
    confirmStatusLoading.value = false
  }
}

// 创建报价单
const handleGenerateQuotation = async () => {
  try {
    await ElMessageBox.confirm('确定要从此报价需求创建报价单吗？', '创建报价单', {
      type: 'warning'
    })

    // 跳转到新建报价单页面，传递报价需求表ID作为参数
    const requestId = route.params.id
    router.push(`/quotations/new?requestId=${requestId}`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('跳转失败:', error)
    }
  }
}

// 转为未确认
const handleUnconfirmStatus = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  unconfirmStatusLoading.value = true
  try {
    const requestId = Number(route.params.id)
    await quotationRequestApi.updateStatus(requestId, { status: '待确认' })

    // 更新本地状态
    form.status = '待确认'

    ElMessage.success('已转为未确认状态，可以继续编辑')
  } catch (error) {
    console.error('转为未确认失败:', error)
    ElMessage.error('转为未确认失败')
  } finally {
    unconfirmStatusLoading.value = false
  }
}

// 导出需求表
const handleExportRequest = async () => {
  if (!isEdit.value) {
    ElMessage.warning('请先保存报价需求')
    return
  }

  exportLoading.value = true
  try {
    const requestId = Number(route.params.id)
    const response = await quotationRequestApi.export(requestId)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `报价需求表_${form.project_name || '未命名'}_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const openStandardizeDialog = async () => {
  if (!form.items.length) {
    ElMessage.warning('没有需要匹配的产品项目')
    return
  }

  // 先预加载产品选项，避免对话框打开后的闪烁
  await ensureProductOptionsLoaded()

  // 复制数据并添加表单字段，同时添加原始索引用于关联
  standardizeItems.value = form.items.map((item, index) => ({
    ...item,
    form_matched_product_id: item.matched_product_id || null,
    form_matched_specification_id: item.matched_product_specification_id || null,
    _originalIndex: index  // 添加原始索引用于关联
  }))

  // 预加载所有已匹配产品的规格选项
  const matchedProductIds = standardizeItems.value
    .map(item => item.matched_product_id)
    .filter(id => id)

  for (const productId of matchedProductIds) {
    await loadSpecificationsForProduct(productId)
  }

  standardizeDialogVisible.value = true
}

const openStandardizeDialogForItem = async (item: any) => {
  // 先预加载产品选项，避免对话框打开后的闪烁
  await ensureProductOptionsLoaded()

  // 复制数据并添加表单字段，同时添加原始索引用于关联
  const originalIndex = form.items.findIndex(formItem => formItem === item)
  standardizeItems.value = [{
    ...item,
    form_matched_product_id: item.matched_product_id || null,
    form_matched_specification_id: item.matched_product_specification_id || null,
    _originalIndex: originalIndex  // 添加原始索引用于关联
  }]

  // 如果已经匹配了产品但没有规格，预加载该产品的规格选项
  if (item.matched_product_id && !item.matched_product_specification_id) {
    await loadSpecificationsForProduct(item.matched_product_id)
  }

  standardizeDialogVisible.value = true
}

const handleDialogProductChange = (row: any, productId: number) => {
  // 立即更新UI状态，避免闪烁
  row.form_matched_product_id = productId
  row.form_matched_specification_id = null

  // 如果选择了产品，预加载规格数据
  if (productId && !productSpecificationsMap.value[productId]) {
    loadSpecificationsForProduct(productId)
  }
}

const loadSpecificationsForProduct = async (productId: number) => {
  if (!productId) return

  // 如果已经加载过，直接返回
  if (productSpecificationsMap.value[productId]) {
    return
  }

  try {
    // 通过获取产品详情来获取规格信息
    const response = await productApi.getById(productId) as any
    const product = response.data || response
    const specifications = product.specifications || []

    // 使用Vue的响应式更新，避免直接赋值导致的闪烁
    productSpecificationsMap.value = {
      ...productSpecificationsMap.value,
      [productId]: specifications
    }

    console.log(`加载产品${productId}的规格:`, specifications)
  } catch (error) {
    console.error('获取产品规格失败:', error)
    // 即使失败也要设置空数组，避免重复请求
    productSpecificationsMap.value = {
      ...productSpecificationsMap.value,
      [productId]: []
    }
    ElMessage.error('获取产品规格失败')
  }
}

const searchProductsForDialog = async (query: string) => {
  try {
    if (query && query.trim()) {
      // 使用综合搜索参数，同时搜索产品名称和型号
      const response = await productApi.getList({
        search: query.trim()
      } as any) as any
      productOptionsForDialog.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } else {
      // 清空搜索词时，显示默认产品列表
      if (productOptionsForDialog.value.length === 0) {
        ensureProductOptionsLoaded()
      }
    }
  } catch (error) {
    console.error('搜索产品失败:', error)
  }
}

const ensureProductOptionsLoaded = async (forceReload = false) => {
  // 如果没有产品选项或强制重新加载，加载默认产品列表
  if (productOptionsForDialog.value.length === 0 || forceReload) {
    try {
      const response = await productApi.getList({} as any) as any
      productOptionsForDialog.value = Array.isArray(response) ? response : (response.data || response.items || [])
    } catch (error) {
      console.error('加载产品选项失败:', error)
    }
  }
}

const submitStandardize = () => {
  // 验证匹配完整性
  const incompleteItems = standardizeItems.value.filter(item =>
    item.form_matched_product_id && !item.form_matched_specification_id
  )

  if (incompleteItems.length > 0) {
    const itemNames = incompleteItems.map(item => item.product_name).join('、')
    ElMessage.warning(`以下产品已选择匹配产品但未选择规格：${itemNames}，请完善匹配信息后再确认`)
    return
  }

  // 将匹配结果应用到原始数据
  standardizeItems.value.forEach(dialogItem => {
    // 使用原始索引直接定位到对应的项目
    const originalItem = form.items[dialogItem._originalIndex]

    if (originalItem) {
      originalItem.matched_product_id = dialogItem.form_matched_product_id
      originalItem.matched_product_specification_id = dialogItem.form_matched_specification_id

      // 设置为手动匹配
      if (dialogItem.form_matched_product_id) {
        originalItem.match_type = 'manual'
      } else {
        originalItem.match_type = null
      }

      // 如果有匹配的规格，更新匹配信息
      if (dialogItem.form_matched_specification_id) {
        const spec = productSpecificationsMap.value[dialogItem.form_matched_product_id]?.find(
          (s: any) => s.id === dialogItem.form_matched_specification_id
        )
        if (spec) {
          originalItem.matched_specification = spec
          // 同时保存匹配的产品信息
          const product = productOptionsForDialog.value.find(p => p.id === dialogItem.form_matched_product_id)
          if (product) {
            originalItem.matched_product = product
          }
        }
      } else {
        // 清空匹配信息
        originalItem.matched_specification = null
        originalItem.matched_product = null
      }
    }
  })

  ElMessage.success('匹配更新成功')
  standardizeDialogVisible.value = false
}

// 获取匹配产品名称
const getMatchedProductName = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.name) {
    return row.matched_product.name
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.name) {
    return row.matched_specification.product.name
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.name) {
      return product.name
    }
  }

  return '未知产品'
}

// 获取匹配产品型号
const getMatchedProductModel = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.model) {
    return row.matched_product.model
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.model) {
    return row.matched_specification.product.model
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.model) {
      return product.model
    }
  }

  return '-'
}

// 获取匹配产品单位
const getMatchedProductUnit = (row: any) => {
  // 优先从matched_product获取
  if (row.matched_product?.unit) {
    return row.matched_product.unit
  }

  // 从matched_specification.product获取
  if (row.matched_specification?.product?.unit) {
    return row.matched_specification.product.unit
  }

  // 从productOptionsForDialog中查找
  if (row.matched_product_id) {
    const product = productOptionsForDialog.value.find(p => p.id === row.matched_product_id)
    if (product?.unit) {
      return product.unit
    }
  }

  return '-'
}

// 安全获取产品规格列表
const getSpecificationsForProduct = (productId: number) => {
  if (!productId) return []
  return productSpecificationsMap.value[productId] || []
}

// 获取匹配规格名称
const getMatchedSpecificationName = (row: any) => {
  // 优先从matched_specification获取
  if (row.matched_specification?.specification) {
    return row.matched_specification.specification
  }

  // 从productSpecificationsMap中查找
  if (row.matched_product_id && row.matched_product_specification_id) {
    const specs = productSpecificationsMap.value[row.matched_product_id] || []
    const spec = specs.find((s: any) => s.id === row.matched_product_specification_id)
    if (spec?.specification) {
      return spec.specification
    }
  }

  return '无规格'
}

// 获取匹配规格价格
const getMatchedSpecificationPrice = (row: any) => {
  // 优先从matched_specification获取
  if (row.matched_specification?.suggested_price) {
    return row.matched_specification.suggested_price
  }

  // 从productSpecificationsMap中查找
  if (row.matched_product_id && row.matched_product_specification_id) {
    const specs = productSpecificationsMap.value[row.matched_product_id] || []
    const spec = specs.find((s: any) => s.id === row.matched_product_specification_id)
    if (spec?.suggested_price) {
      return spec.suggested_price
    }
  }

  return '0.00'
}



// 初始化
onMounted(() => {
  if (isEdit.value) {
    getRequestDetail()
  }
})
</script>

<style lang="scss" scoped>
.quotation-request-edit {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

.quotation-request-form {
  .table-operations {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operations-left {
      display: flex;
      align-items: center;
    }

    .operations-right {
      display: flex;
      align-items: center;
    }
  }
}

.el-form-item__extra_info {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.upload-demo {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.el-icon--upload) {
    font-size: 67px;
    color: var(--el-text-color-placeholder);
    margin: 40px 0 16px;
    line-height: 50px;
  }

  :deep(.el-upload__text) {
    color: var(--el-text-color-regular);
    font-size: 14px;
    text-align: center;

    em {
      color: var(--el-color-primary);
      font-style: normal;
    }
  }
}

.matching-active {
  background-color: var(--el-color-danger) !important;
  border-color: var(--el-color-danger) !important;
  color: white !important;
}

.matching-mode {
  .matching-header {
    margin-bottom: 20px;
  }

  .matching-operations {
    margin-bottom: 20px;
  }

  .matched-product {
    font-weight: bold;
    color: var(--el-color-primary);
  }

  .matched-spec {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
  }

  .matched-price {
    font-size: 12px;
    color: var(--el-color-success);
    margin-top: 2px;
  }

  .text-muted {
    color: var(--el-text-color-placeholder);
  }

  .unmatched-section {
    .match-actions {
      margin-top: 8px;
    }

    .match-notes {
      margin: 4px 0;
      padding: 4px 8px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      border-left: 3px solid var(--el-text-color-placeholder);
      font-size: 12px;
    }
  }

  .original-product {
    margin-bottom: 6px;
    padding: 4px 8px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    font-size: 12px;
  }

  .matched-product {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;

    .el-icon {
      color: var(--el-color-success);
    }
  }

  .matched-info {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-color-success);

    .el-icon {
      font-size: 12px;
    }

    span {
      color: var(--el-color-success);
    }
  }

  .table-operations {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .operations-left {
      display: flex;
      align-items: center;
    }

    .operations-right {
      display: flex;
      align-items: center;
    }

    .matching-active {
      background-color: var(--el-color-danger);
      border-color: var(--el-color-danger);
      color: white;
    }
  }

  .quantity-input {
    :deep(.el-input__inner) {
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      background-color: #f8f9fa;
      border: 2px solid #e9ecef;
    }

    :deep(.el-input__inner:focus) {
      background-color: #fff;
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      background-color: #f8f9fa;
      border-color: #e9ecef;
      font-weight: bold;
    }

    :deep(.el-input-number__decrease:hover),
    :deep(.el-input-number__increase:hover) {
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      color: white;
    }
  }
}

.light-blue-btn {
  background-color: #e1f5fe !important;
  border-color: #81d4fa !important;
  color: #0277bd !important;
}

.light-blue-btn:hover {
  background-color: #b3e5fc !important;
  border-color: #4fc3f7 !important;
  color: #01579b !important;
}
</style>

#!/usr/bin/env python3
"""
数据库清理脚本
删除所有订单、发货单、退货单及相关数据，重置为干净状态
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def clear_database():
    """清空数据库中的所有业务数据"""
    
    # 数据库文件路径
    db_path = project_root / 'instance' / 'project.db'
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("🗑️  开始清理数据库...")
        
        # 禁用外键约束检查
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # 要清理的表（按依赖关系排序）
        tables_to_clear = [
            # 子表先删除
            'return_order_items',           # 退货单明细
            'delivery_note_items',          # 发货单明细  
            'order_products',               # 订单产品
            'order_status_history',         # 订单状态历史
            'statement_items',              # 对账单明细
            'payment_records',              # 收款记录
            
            # 主表后删除
            'return_orders',                # 退货单
            'delivery_notes',               # 发货单
            'statements',                   # 对账单
            'orders',                       # 订单
        ]
        
        # 清理每个表
        for table in tables_to_clear:
            try:
                # 检查表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table,))
                
                if cursor.fetchone():
                    # 获取删除前的记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count_before = cursor.fetchone()[0]
                    
                    # 删除所有记录
                    cursor.execute(f"DELETE FROM {table}")
                    
                    # 重置自增ID
                    cursor.execute(f"""
                        DELETE FROM sqlite_sequence 
                        WHERE name = '{table}'
                    """)
                    
                    print(f"✅ 清理表 {table}: 删除了 {count_before} 条记录")
                else:
                    print(f"⚠️  表 {table} 不存在，跳过")
                    
            except sqlite3.Error as e:
                print(f"❌ 清理表 {table} 失败: {e}")
        
        # 重新启用外键约束检查
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 提交事务
        conn.commit()
        
        print("\n🎉 数据库清理完成！")
        print("📊 清理统计:")
        
        # 验证清理结果
        for table in tables_to_clear:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} 条记录")
            except sqlite3.Error:
                print(f"   {table}: 表不存在")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 50)
    print("🗑️  EMB数据库清理工具")
    print("=" * 50)
    
    # 确认操作
    confirm = input("\n⚠️  警告：此操作将删除所有订单、发货单、退货单数据！\n是否继续？(y/N): ")
    
    if confirm.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行清理
    success = clear_database()
    
    if success:
        print("\n✅ 数据库清理成功！现在可以重新测试系统功能。")
    else:
        print("\n❌ 数据库清理失败！请检查错误信息。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单状态系统重构：完全废弃status字段迁移脚本
将现有的单一status字段完全迁移到双状态系统（order_status + payment_status）

执行步骤：
1. 数据备份
2. 数据迁移：status -> order_status
3. 财务状态计算：根据金额计算payment_status
4. 数据验证
5. 添加约束
6. 可选：删除status字段

运行方式：
cd backend
python migrations/deprecate_status_field.py

注意：运行前请备份数据库！
"""

import sqlite3
import sys
import os
import json
from datetime import datetime
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_db_connection():
    """获取数据库连接"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    if not os.path.exists(db_path):
        print(f"❌ 错误: 数据库文件不存在: {db_path}")
        sys.exit(1)
    return sqlite3.connect(db_path)

def backup_database():
    """备份数据库"""
    try:
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 复制数据库文件
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库备份完成: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 数据库备份失败: {str(e)}")
        sys.exit(1)

def check_current_status():
    """检查当前数据库状态"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查表结构
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        has_status = any(col[1] == 'status' for col in columns)
        has_order_status = any(col[1] == 'order_status' for col in columns)
        has_payment_status = any(col[1] == 'payment_status' for col in columns)
        
        print("📊 当前数据库状态:")
        print(f"   status字段: {'✅ 存在' if has_status else '❌ 不存在'}")
        print(f"   order_status字段: {'✅ 存在' if has_order_status else '❌ 不存在'}")
        print(f"   payment_status字段: {'✅ 存在' if has_payment_status else '❌ 不存在'}")
        
        if not has_order_status or not has_payment_status:
            print("❌ 错误: 双状态字段不存在，请先运行 add_dual_status_fields.sql")
            sys.exit(1)
            
        # 检查现有数据
        cursor.execute("SELECT COUNT(*) FROM orders")
        total_orders = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE status IS NOT NULL AND status != ''")
        orders_with_status = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_status IS NULL OR order_status = ''")
        orders_need_migration = cursor.fetchone()[0]
        
        print(f"   总订单数: {total_orders}")
        print(f"   有status数据的订单: {orders_with_status}")
        print(f"   需要迁移的订单: {orders_need_migration}")
        
        return {
            'has_status': has_status,
            'has_order_status': has_order_status,
            'has_payment_status': has_payment_status,
            'total_orders': total_orders,
            'orders_need_migration': orders_need_migration
        }
        
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {str(e)}")
        sys.exit(1)
    finally:
        conn.close()

def migrate_status_data():
    """迁移状态数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("🚀 开始迁移状态数据...")
        
        # 获取需要迁移的订单
        cursor.execute("""
            SELECT id, order_number, status, total_amount, paid_amount 
            FROM orders 
            WHERE (order_status IS NULL OR order_status = '' OR order_status = '待确认')
            AND status IS NOT NULL AND status != ''
        """)
        
        orders = cursor.fetchall()
        print(f"📋 找到 {len(orders)} 个需要迁移的订单")
        
        if len(orders) == 0:
            print("✅ 没有需要迁移的数据")
            return
        
        # 状态映射规则
        status_mapping = {
            # 标准物流状态（直接映射）
            '待确认': '待确认',
            '已确认': '已确认', 
            '生产中': '生产中',
            '待发货': '待发货',
            '发货中': '部分发货',  # 发货中 -> 部分发货
            '部分发货': '部分发货',
            '全部发货': '全部发货',
            '已完成': '已完成',
            '已取消': '已取消',
            
            # 对账相关状态（重置为全部发货）
            '待对账': '全部发货',
            '部分对账': '全部发货', 
            '全部对账': '全部发货',
            
            # 收款相关状态（重置为已完成）
            '待收款': '已完成',
            '部分收款': '已完成',
            '已结清': '已完成',
        }
        
        migrated_count = 0
        
        for order in orders:
            order_id, order_number, current_status, total_amount, paid_amount = order
            
            # 映射物流状态
            if current_status in status_mapping:
                new_order_status = status_mapping[current_status]
            else:
                print(f"⚠️  未知状态 '{current_status}' (订单 {order_number})，设置为 '待确认'")
                new_order_status = '待确认'
            
            # 计算财务状态
            total = float(total_amount) if total_amount else 0
            paid = float(paid_amount) if paid_amount else 0
            
            if paid == 0:
                new_payment_status = '未收款'
            elif paid < total:
                new_payment_status = '部分收款'
            elif paid >= total:
                new_payment_status = '已收款'
            else:
                new_payment_status = '未收款'
            
            # 更新订单状态
            cursor.execute("""
                UPDATE orders 
                SET order_status = ?, payment_status = ?
                WHERE id = ?
            """, (new_order_status, new_payment_status, order_id))
            
            migrated_count += 1
            print(f"✅ 订单 {order_number}: {current_status} -> 物流:{new_order_status}, 财务:{new_payment_status}")
        
        conn.commit()
        print(f"🎉 成功迁移 {migrated_count} 个订单的状态数据")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 迁移失败: {str(e)}")
        raise
    finally:
        conn.close()

def add_constraints():
    """添加数据库约束"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("🔒 添加数据库约束...")
        
        # 检查是否已存在约束
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        # 添加物流状态约束
        try:
            cursor.execute("""
                ALTER TABLE orders ADD CONSTRAINT chk_order_status 
                CHECK (order_status IN ('待确认', '已确认', '生产中', '待发货', '部分发货', '全部发货', '已完成', '已取消'))
            """)
            print("✅ 添加物流状态约束成功")
        except sqlite3.OperationalError as e:
            if "already exists" in str(e) or "duplicate" in str(e):
                print("ℹ️  物流状态约束已存在")
            else:
                print(f"⚠️  添加物流状态约束失败: {str(e)}")
        
        # 添加财务状态约束
        try:
            cursor.execute("""
                ALTER TABLE orders ADD CONSTRAINT chk_payment_status 
                CHECK (payment_status IN ('未收款', '部分收款', '已收款'))
            """)
            print("✅ 添加财务状态约束成功")
        except sqlite3.OperationalError as e:
            if "already exists" in str(e) or "duplicate" in str(e):
                print("ℹ️  财务状态约束已存在")
            else:
                print(f"⚠️  添加财务状态约束失败: {str(e)}")
        
        conn.commit()
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 添加约束失败: {str(e)}")
        raise
    finally:
        conn.close()

def verify_migration():
    """验证迁移结果"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("🔍 验证迁移结果...")
        
        # 检查迁移统计
        cursor.execute("""
            SELECT 
                order_status,
                payment_status,
                COUNT(*) as count
            FROM orders 
            GROUP BY order_status, payment_status
            ORDER BY order_status, payment_status
        """)
        
        results = cursor.fetchall()
        print("\n📊 迁移结果统计:")
        print("物流状态 | 财务状态 | 数量")
        print("-" * 45)
        for row in results:
            print(f"{row[0]:<10} | {row[1]:<8} | {row[2]}")
        
        # 检查是否有空值
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_status IS NULL OR order_status = ''")
        null_order_status = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE payment_status IS NULL OR payment_status = ''")
        null_payment_status = cursor.fetchone()[0]
        
        if null_order_status > 0 or null_payment_status > 0:
            print(f"⚠️  警告: 发现空状态数据 - 物流状态:{null_order_status}, 财务状态:{null_payment_status}")
        else:
            print("✅ 所有订单都有有效的状态数据")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
    finally:
        conn.close()

def drop_status_field():
    """删除status字段（可选操作）"""
    response = input("\n❓ 是否删除原有的status字段？(y/N): ").strip().lower()
    if response != 'y':
        print("ℹ️  保留status字段作为备份")
        return
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        print("🗑️  删除status字段...")
        
        # SQLite不支持直接删除列，需要重建表
        # 这里只是标记为废弃，不实际删除
        cursor.execute("ALTER TABLE orders RENAME COLUMN status TO status_deprecated")
        conn.commit()
        print("✅ status字段已重命名为status_deprecated")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 删除status字段失败: {str(e)}")
        print("ℹ️  建议手动处理或保留作为备份")
    finally:
        conn.close()

def main():
    """主函数"""
    print("🚀 订单状态系统重构：废弃status字段迁移")
    print("=" * 50)
    
    # 1. 检查当前状态
    status_info = check_current_status()
    
    if status_info['orders_need_migration'] == 0:
        print("✅ 所有订单已完成迁移")
        verify_migration()
        return
    
    # 2. 备份数据库
    backup_path = backup_database()
    
    # 3. 迁移数据
    migrate_status_data()
    
    # 4. 添加约束
    add_constraints()
    
    # 5. 验证结果
    verify_migration()
    
    # 6. 可选：删除status字段
    drop_status_field()
    
    print("\n🎉 迁移完成！")
    print(f"📁 备份文件: {backup_path}")
    print("📝 下一步: 修改后端代码以使用新的双状态系统")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
更新订单收款状态脚本
根据发货单和退货单的结清状态重新计算订单的收款状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.order import Order

def update_order_payment_status():
    """更新所有订单的收款状态"""
    app = create_app()
    
    with app.app_context():
        print("开始更新订单收款状态...")
        
        # 获取所有订单
        orders = Order.query.all()
        updated_count = 0
        
        for order in orders:
            old_status = order.payment_status
            
            # 重新计算收款状态
            order.auto_update_payment_status()
            
            if order.payment_status != old_status:
                print(f"订单 {order.order_number}: {old_status} → {order.payment_status}")
                updated_count += 1
        
        # 提交更改
        try:
            db.session.commit()
            print(f"\n✅ 更新完成！共更新了 {updated_count} 个订单的收款状态")
        except Exception as e:
            db.session.rollback()
            print(f"\n❌ 更新失败: {str(e)}")
            return False
        
        # 显示更新后的状态统计
        print("\n📊 更新后的订单收款状态统计:")
        status_counts = db.session.query(
            Order.payment_status, 
            db.func.count(Order.id)
        ).group_by(Order.payment_status).all()
        
        for status, count in status_counts:
            print(f"   {status}: {count} 个订单")
        
        return True

if __name__ == '__main__':
    success = update_order_payment_status()
    sys.exit(0 if success else 1)

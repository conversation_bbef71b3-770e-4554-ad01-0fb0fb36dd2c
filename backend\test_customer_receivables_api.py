#!/usr/bin/env python3
"""
测试客户应收账款API
"""

import requests
import json

def test_customer_receivables_api():
    """测试客户应收账款API"""
    print("🧪 测试客户应收账款API")
    print("=" * 50)
    
    try:
        # 测试客户ID=1的应收账款
        url = "http://localhost:5001/api/v1/customers/1/receivables"
        response = requests.get(url)
        
        print(f"📡 API调用: {url}")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"📋 响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 解析数据
            receivable_data = data.get('data', {})
            print(f"\n💰 应收账款汇总:")
            print(f"   客户: {receivable_data.get('customer_name')}")
            print(f"   总应收金额: ¥{receivable_data.get('total_amount', 0):,.2f}")
            print(f"   未结清金额: ¥{receivable_data.get('unsettled_amount', 0):,.2f}")
            print(f"   已结清金额: ¥{receivable_data.get('settled_amount', 0):,.2f}")
            print(f"   明细记录数: {len(receivable_data.get('details', []))}")
            
            # 显示明细
            details = receivable_data.get('details', [])
            if details:
                print(f"\n📝 应收账款明细:")
                for i, detail in enumerate(details, 1):
                    print(f"   {i}. 来源: {detail.get('source_type')}")
                    print(f"      金额: ¥{detail.get('amount', 0):,.2f}")
                    print(f"      状态: {detail.get('settlement_status')}")
                    
                    source_info = detail.get('source_info', {})
                    if source_info:
                        print(f"      对账单号: {source_info.get('statement_number')}")
                        print(f"      对账单状态: {source_info.get('status')}")
                        print(f"      调整后金额: ¥{source_info.get('adjusted_total_amount', 0):,.2f}")
                        print(f"      已付金额: ¥{source_info.get('paid_amount', 0):,.2f}")
                    print()
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    test_customer_receivables_api()

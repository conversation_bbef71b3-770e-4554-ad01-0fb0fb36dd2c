<template>
  <div class="quotation-request-list">
    <!-- 搜索表单 -->
    <el-card class="search-form-card mb-20">
      <el-form ref="searchFormRef" :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="需求编号" prop="request_number">
          <el-input v-model="searchForm.request_number" placeholder="请输入需求编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称" prop="customer_name">
          <el-input v-model="searchForm.customer_name" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="项目名称" prop="project_name">
          <el-input v-model="searchForm.project_name" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待确认" value="待确认" />
            <el-option label="已确认" value="已确认" />
          </el-select>
        </el-form-item>
        <el-form-item label="期望日期" prop="date_range">
          <el-date-picker
            v-model="searchForm.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearchForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operator-card mb-20">
      <div class="operator-content">
        <div class="left-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增报价需求
          </el-button>
          <el-button type="success" @click="handleBatchExport" :disabled="selectedRequests.length === 0">
            <el-icon><Download /></el-icon>
            批量导出
          </el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedRequests.length === 0">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>

        <div class="right-actions">
          <!-- 卡片详细程度切换 -->
          <el-button-group>
            <el-button
              :type="cardMode === 'detailed' ? 'primary' : ''"
              @click="cardMode = 'detailed'"
              title="详细模式"
            >
              详细
            </el-button>
            <el-button
              :type="cardMode === 'simple' ? 'primary' : ''"
              @click="cardMode = 'simple'"
              title="简化模式"
            >
              简化
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 报价需求列表 -->
    <el-card>
      <!-- 卡片视图 -->
      <div v-loading="loading">
        <!-- 批量选择工具栏 -->
        <div v-if="selectedRequests.length > 0" class="batch-selection-bar">
          <span>已选择 {{ selectedRequests.length }} 个报价需求</span>
          <el-button size="small" @click="clearSelection">清空选择</el-button>
        </div>

        <!-- 卡片列表 -->
        <div class="request-cards-list">
          <div
            v-for="request in tableData"
            :key="request.id"
            :class="['request-card', { 'selected': isRequestSelected(request) }]"
            @click="handleCardClick(request)"
          >
            <!-- 选择框 -->
            <div class="card-checkbox" @click.stop>
              <el-checkbox
                :model-value="isRequestSelected(request)"
                @change="handleCardSelection(request, $event)"
              />
            </div>

            <!-- 详细模式卡片内容 -->
            <div v-if="cardMode === 'detailed'" class="card-content detailed">
              <!-- 第一行：需求编号、客户、项目、状态 -->
              <div class="card-row-1">
                <div class="left-info">
                  <div class="request-number">{{ request.request_number }}</div>
                  <div class="customer-project-status">
                    <span class="customer">{{ request.customer_name || 'N/A' }}</span>
                    <span class="separator">|</span>
                    <span class="project" :title="request.project_name">{{ request.project_name }}</span>
                    <div class="status-tags">
                      <el-tag :type="getStatusType(request.status)" size="default" class="status-tag-prominent request-status">
                        {{ request.status }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第二行：期望日期、创建时间和关联报价单 -->
              <div class="card-row-2">
                <div class="left-details">
                  <div class="dates-info">
                    <div class="date-group">
                      <span class="date-label">期望日期</span>
                      <span class="date-value">{{ request.expected_date ? formatDate(request.expected_date) : 'N/A' }}</span>
                    </div>
                    <div class="date-group">
                      <span class="date-label">创建时间</span>
                      <span class="date-value">{{ request.created_at ? formatDateTime(request.created_at) : 'N/A' }}</span>
                    </div>
                  </div>
                  <div class="quotations-info">
                    <div class="quotation-group">
                      <span class="quotation-label">关联报价单</span>
                      <div v-if="request.quotations && request.quotations.length > 0" class="quotation-links">
                        <el-tag
                          v-for="quotation in request.quotations"
                          :key="quotation.id"
                          :type="getQuotationStatusType(quotation.status)"
                          size="small"
                          class="quotation-tag"
                          @click.stop="handleViewQuotation(quotation)"
                        >
                          {{ quotation.quotation_number }}
                        </el-tag>
                      </div>
                      <span v-else class="no-quotations">暂无关联</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 第三行：操作按钮 -->
              <div class="card-actions detailed" @click.stop>
                <!-- 待确认状态：可以编辑、删除、确认 -->
                <template v-if="request.status === '待确认'">
                  <el-button type="primary" size="small" @click="handleEdit(request)" title="编辑需求">
                    编辑
                  </el-button>
                  <el-button type="success" size="small" @click="handleConfirm(request)" title="确认需求">
                    确认
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDelete(request)" title="删除需求">
                    删除
                  </el-button>
                </template>

                <!-- 已确认且未关联报价单：可以创建报价单和取消确认 -->
                <template v-else-if="request.status === '已确认' && (!request.quotations || request.quotations.length === 0)">
                  <el-button type="success" size="small" @click="handleCreateQuotation(request)" title="创建报价单">
                    创建报价单
                  </el-button>
                  <el-button type="warning" size="small" @click="handleCancelConfirm(request)" title="取消确认">
                    取消确认
                  </el-button>
                </template>

                <!-- 已确认且已关联报价单：无需额外操作按钮，点击卡片即可查看 -->
                <template v-else-if="request.status === '已确认' && request.quotations && request.quotations.length > 0">
                  <!-- 移除查看详情按钮，用户可以直接点击卡片查看 -->
                </template>
              </div>
            </div>

            <!-- 简化模式卡片内容 -->
            <div v-else class="card-content simple">
              <div class="simple-row">
                <div class="simple-left">
                  <div class="request-number">{{ request.request_number }}</div>
                  <div class="customer-project">
                    <span class="customer">{{ request.customer_name || 'N/A' }}</span>
                    <span class="separator">|</span>
                    <span class="project">{{ request.project_name }}</span>
                  </div>
                </div>
                <div class="simple-right">
                  <el-tag :type="getStatusType(request.status)" size="small">
                    {{ request.status }}
                  </el-tag>
                  <div class="simple-actions" @click.stop>
                    <el-dropdown trigger="click">
                      <el-button type="text" size="small">
                        操作 <el-icon><ArrowDown /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-if="request.status === '待确认'" @click="handleEdit(request)">
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item v-if="request.status === '待确认'" @click="handleConfirm(request)">
                            确认
                          </el-dropdown-item>
                          <el-dropdown-item v-if="request.status === '待确认'" @click="handleDelete(request)" divided>
                            删除
                          </el-dropdown-item>
                          <el-dropdown-item v-if="request.status === '已确认' && (!request.quotations || request.quotations.length === 0)" @click="handleCreateQuotation(request)">
                            创建报价单
                          </el-dropdown-item>
                          <el-dropdown-item v-if="request.status === '已确认' && (!request.quotations || request.quotations.length === 0)" @click="handleCancelConfirm(request)">
                            取消确认
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <el-pagination
        class="pagination-container"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { Plus, Download, Delete, ArrowDown } from '@element-plus/icons-vue';
import { listQuotationRequests as getQuotationRequests, generateQuotationFromRequest as apiCreateQuotationFromRequest, quotationRequestApi } from '@/api/quotation';
import { formatDate, formatDateTime, formatCurrency } from '@/utils/format';
import type { QuotationRequest, Customer } from '@/types/api';

const router = useRouter()

// 搜索表单引用
const searchFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  request_number: '',
  customer_name: '',
  project_name: '',
  status: '',
  date_range: null as [string, string] | null
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 报价需求列表数据
const tableData = ref<QuotationRequest[]>([])
const selectedRequests = ref([])
const loading = ref(false)

// 卡片模式
const cardMode = ref('detailed') // 'detailed' | 'simple'

// 兼容旧的分页变量
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage,
      per_page: pagination.pageSize,
      ...searchForm
    }
    const response = await getQuotationRequests(params) as any

    console.log('报价需求列表响应:', response)

    // 处理API响应数据
    if (Array.isArray(response)) {
      // 如果响应直接是数组（被request拦截器处理过）
      tableData.value = response.map(item => ({
        ...item,
        status: translateStatus(item.status || ''),
        exporting: false
      }))
      pagination.total = response.length
    } else if (response && typeof response === 'object') {
      // 如果响应是对象格式
      const data = response.data || response.items || []
      tableData.value = data.map((item: any) => ({
        ...item,
        status: translateStatus(item.status || ''),
        exporting: false
      }))
      pagination.total = response.pagination?.total || response.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
    }

    // 兼容旧的分页变量
    total.value = pagination.total
    currentPage.value = pagination.currentPage
    pageSize.value = pagination.pageSize
  } catch (error) {
    console.error('获取报价需求列表失败:', error)
    ElMessage.error('获取报价需求列表失败')
    tableData.value = [] // 清空表格数据
    pagination.total = 0      // 重置总数
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 基础操作函数
const handleAdd = () => {
  router.push('/quotation-requests/new')
}

const handleEdit = (row: QuotationRequest) => {
  router.push(`/quotation-requests/edit/${row.id}`)
}



const handleViewDetails = (row: QuotationRequest) => {
  // 检查是否有文本被选中，如果有则不触发跳转
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    return;
  }

  if (row.id) {
    router.push(`/quotation-requests/view/${row.id}`);
  }
};

const handleDelete = async (row: QuotationRequest) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报价需求吗？', '确认删除', {
      type: 'warning'
    })

    // 调用删除API
    await quotationRequestApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 搜索和重置
const handleSearch = () => {
  pagination.currentPage = 1
  fetchData()
}

const resetSearchForm = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields()
  }
  Object.assign(searchForm, {
    request_number: '',
    customer_name: '',
    project_name: '',
    status: '',
    date_range: null
  })
  pagination.currentPage = 1
  fetchData()
}

// 兼容旧的重置函数
const handleReset = () => {
  resetSearchForm()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchData()
}

// 状态处理
const statusMap: Record<string, string> = {
  'pending': '待确认',
  'confirmed': '已确认'
}

const translateStatus = (status: string): string => {
  return statusMap[status.toLowerCase()] || statusMap[status] || status;
};

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '待确认': 'warning',
    '已确认': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取报价单状态类型
const getQuotationStatusType = (status: string) => {
  switch (status) {
    case '待确认':
      return 'warning'
    case '已确认':
      return 'success'
    case '已拒绝':
      return 'danger'
    case '已过期':
      return 'info'
    default:
      return 'info'
  }
}

// 查看报价单详情
const handleViewQuotation = (quotation: any) => {
  router.push(`/quotations/view/${quotation.id}`)
}

// 确认需求表
const handleConfirm = async (row: any) => {
  try {
    // 首先获取详细信息，检查产品匹配状态
    const detailResponse = await quotationRequestApi.getById(row.id)
    const requestDetail = detailResponse.data || detailResponse

    // 检查是否有未匹配的产品
    const unmatchedItems = requestDetail.items?.filter(item => !item.matched_product_specification_id) || []

    if (unmatchedItems.length > 0) {
      const unmatchedNames = unmatchedItems.map(item => item.product_name || '未命名产品').join('、')
      ElMessage.warning(`以下产品尚未完全匹配，请先完成产品匹配：${unmatchedNames}`)
      return
    }

    await ElMessageBox.confirm(
      '确认后将不能再修改需求表内容，是否继续？',
      '确认操作',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新状态
    await quotationRequestApi.updateStatus(row.id, { status: '已确认' })
    ElMessage.success('需求表确认成功')

    // 刷新列表
    fetchData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '确认失败')
    }
  }
}

// 创建报价单
const handleCreateQuotation = (row: any) => {
  router.push(`/quotations/new?requestId=${row.id}`)
}

// 取消确认
const handleCancelConfirm = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      '取消确认后需求表将变为待确认状态，是否继续？',
      '取消确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用API更新状态
    await quotationRequestApi.updateStatus(row.id, { status: '待确认' })
    ElMessage.success('已取消确认')

    // 刷新列表
    fetchData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '取消确认失败')
    }
  }
}

// 卡片选择相关函数
const isRequestSelected = (request: any) => {
  return selectedRequests.value.some((selected: any) => selected.id === request.id)
}

const handleCardClick = (request: any) => {
  handleViewDetails(request)
}

const handleCardSelection = (request: any, checked: boolean) => {
  if (checked) {
    if (!isRequestSelected(request)) {
      selectedRequests.value.push(request)
    }
  } else {
    selectedRequests.value = selectedRequests.value.filter((selected: any) => selected.id !== request.id)
  }
}

const clearSelection = () => {
  selectedRequests.value = []
}

// 批量操作函数
const handleBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRequests.value.length} 个报价需求吗？`, '批量删除', {
      type: 'warning'
    })

    // 这里应该调用批量删除API
    ElMessage.success('批量删除成功')
    clearSelection()
    fetchData()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.quotation-request-list {
  padding: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

// 搜索表单样式
.search-form-card {
  .el-form {
    margin-bottom: 0;
  }
}

// 操作按钮区域样式
.operator-card {
  .operator-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

// 批量选择工具栏
.batch-selection-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

// 卡片列表样式
.request-cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px; // 增加卡片间距
  margin-bottom: 20px;

  // 报价需求卡片样式
  .request-card {
    position: relative;
    width: 100%;
    border: 2px solid #e8eaed; // 增加边框宽度，使用更柔和的颜色
    border-radius: 12px; // 增加圆角
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); // 添加轻微阴影
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 4px; // 额外的底部间距

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15); // 增强悬停阴影
      transform: translateY(-1px); // 轻微上移效果
    }

    &.selected {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2), 0 4px 16px rgba(64, 158, 255, 0.1); // 双重阴影效果
      transform: translateY(-1px);
    }

    .card-checkbox {
      position: absolute;
      top: 16px;
      left: 16px;
      z-index: 2;
    }

    .card-content {
      padding: 12px 16px;
      padding-left: 44px; // 为选择框留出空间

      &.detailed {
        padding: 14px 16px;
        padding-left: 44px;
      }

      &.simple {
        padding: 12px 16px;
        padding-left: 44px;
      }

      // 详细模式样式
      .card-row-1 {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .left-info {
          flex: 1;
          min-width: 0;

          .request-number {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .customer-project-status {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .customer {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              flex-shrink: 0;
            }

            .separator {
              color: #dcdfe6;
              font-size: 12px;
            }

            .project {
              font-size: 13px;
              color: #606266;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 200px;
              margin-right: 12px;
            }

            .status-tags {
              display: flex;
              gap: 4px;
              flex-shrink: 0;

              .status-tag-prominent {
                font-weight: 600;
                border-radius: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                border: 2px solid transparent;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                }

                // 报价需求状态 - 统一大小
                &.request-status {
                  font-size: 13px;
                  padding: 5px 12px;
                  font-weight: 600;
                  letter-spacing: 0.3px;

                  &.el-tag--success {
                    background: linear-gradient(135deg, #67c23a, #85ce61);
                    border-color: #67c23a;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(103, 194, 58, 0.25);
                  }

                  &.el-tag--warning {
                    background: linear-gradient(135deg, #e6a23c, #ebb563);
                    border-color: #e6a23c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(230, 162, 60, 0.25);
                  }

                  &.el-tag--danger {
                    background: linear-gradient(135deg, #f56c6c, #f78989);
                    border-color: #f56c6c;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.25);
                  }

                  &.el-tag--info {
                    background: linear-gradient(135deg, #909399, #a6a9ad);
                    border-color: #909399;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(144, 147, 153, 0.25);
                  }

                  &.el-tag--primary {
                    background: linear-gradient(135deg, #409eff, #66b1ff);
                    border-color: #409eff;
                    color: #fff;
                    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
                  }
                }
              }
            }
          }
        }
      }

      .card-row-2 {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        padding: 8px 0;
        border-top: 1px solid #f0f0f0;

        .left-details {
          flex: 1;
          display: flex;
          gap: 32px;

          .dates-info {
            display: flex;
            gap: 24px;

            .date-group {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .date-label {
                font-size: 12px;
                color: #909399;
              }

              .date-value {
                font-size: 13px;
                color: #606266;
                font-weight: 500;
              }
            }
          }

          .quotations-info {
            .quotation-group {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .quotation-label {
                font-size: 12px;
                color: #909399;
              }

              .quotation-links {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
              }

              .quotation-tag {
                cursor: pointer;
                transition: all 0.2s;

                &:hover {
                  transform: scale(1.05);
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
              }

              .no-quotations {
                color: #999;
                font-size: 12px;
              }
            }
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        &.detailed {
          justify-content: flex-start;
        }
      }

      // 简化模式样式
      .simple-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .simple-left {
          flex: 1;
          min-width: 0;

          .request-number {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .customer-project {
            font-size: 12px;
            color: #909399;

            .customer {
              color: #606266;
            }

            .separator {
              margin: 0 8px;
              color: #dcdfe6;
            }

            .project {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .simple-right {
          display: flex;
          align-items: center;
          gap: 12px;

          .simple-actions {
            .el-dropdown {
              .el-button {
                padding: 0;
                border: none;
                background: none;
                color: #409eff;

                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>

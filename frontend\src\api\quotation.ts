import request from './request'

// 报价单相关API
export const quotationApi = {
  // 获取报价单列表
  getList: (params: any) => {
    return request.get('/quotations', { params })
  },

  // 获取报价单详情
  getById: (id: number) => {
    return request.get(`/quotations/${id}`)
  },

  // 创建报价单
  create: (data: any) => {
    return request.post('/quotations', data)
  },

  // 更新报价单
  update: (id: number, data: any) => {
    return request.put(`/quotations/${id}`, data)
  },

  // 删除报价单
  delete: (id: number) => {
    return request.delete(`/quotations/${id}`)
  },

  // 更新报价单状态
  updateStatus: (id: number, status: string) => {
    return request.put(`/quotations/${id}/status`, { status })
  },

  // 导出报价单
  export: (id: number, config: any = {}) => {
    const params = {
      format: config.format || 'pdf',
      columns: config.columns ? config.columns.join(',') : undefined,
      include_header: config.include_header !== undefined ? config.include_header : true
    }
    // 移除undefined的参数
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key])

    return request.get(`/quotations/${id}/export`, {
      params,
      responseType: 'blob'
    })
  },

  // 从报价单创建订单
  createOrder: (id: number, data?: any) => {
    return request.post(`/orders/quotations/${id}/create-order`, data)
  }
}

// 报价需求相关API
export const quotationRequestApi = {
  // 获取报价需求列表
  getList: (params: any) => {
    return request.get('/quotations/requests', { params })
  },

  // 获取报价需求详情
  getById: (id: number) => {
    return request.get(`/quotations/requests/${id}`)
  },

  // 创建报价需求
  create: (data: any) => {
    return request.post('/quotations/requests', data)
  },

  // 更新报价需求
  update: (id: number, data: any) => {
    return request.put(`/quotations/requests/${id}`, data)
  },

  // 删除报价需求
  delete: (id: number) => {
    return request.delete(`/quotations/requests/${id}`)
  },

  // 更新报价需求状态
  updateStatus: (id: number, data: any) => {
    return request.put(`/quotations/requests/${id}/status`, data)
  },

  // 从报价需求生成报价单
  generateQuotation: (id: number, data: any) => {
    return request.post(`/quotations/requests/${id}/generate-quotation`, data)
  },

  // 下载询价单模板
  downloadTemplate: () => {
    return request.get('/quotations/requests/template', {
      responseType: 'blob'
    })
  },

  // 自动匹配报价需求中的所有产品
  autoMatch: (id: number) => {
    return request.post(`/quotations/requests/${id}/auto-match`)
  },

  // 导出报价需求表
  export: (id: number, config: any = {}) => {
    const params = {
      format: config.format || 'xlsx',
      columns: config.columns ? config.columns.join(',') : undefined,
      include_header: config.include_header !== undefined ? config.include_header : true
    }
    // 移除undefined的参数
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key])

    return request.get(`/quotations/requests/${id}/export`, {
      params,
      responseType: 'blob'
    })
  }
}

// 报价模板相关API
export const quotationTemplateApi = {
  // 获取报价模板列表
  getList: (params: any) => {
    return request.get('/quotations/templates', { params })
  },

  // 获取报价模板详情
  getById: (id: number) => {
    return request.get(`/quotations/templates/${id}`)
  },

  // 创建报价模板
  create: (data: any) => {
    return request.post('/quotations/templates', data)
  },

  // 更新报价模板
  update: (id: number, data: any) => {
    return request.put(`/quotations/templates/${id}`, data)
  },

  // 删除报价模板
  delete: (id: number) => {
    return request.delete(`/quotations/templates/${id}`)
  },

  // 从模板创建报价单
  createQuotation: (id: number, data: any) => {
    return request.post(`/quotations/templates/${id}/create-quotation`, data)
  }
}

// 兼容原项目的API函数
export const listQuotations = quotationApi.getList
export const getQuotation = quotationApi.getById
export const createQuotation = quotationApi.create
export const updateQuotation = quotationApi.update
export const deleteQuotation = quotationApi.delete
export const updateQuotationStatus = quotationApi.updateStatus
export const exportQuotation = quotationApi.export
export const createOrderFromQuotation = quotationApi.createOrder

export const listQuotationRequests = quotationRequestApi.getList
export const getQuotationRequest = quotationRequestApi.getById
export const createQuotationRequest = quotationRequestApi.create
export const updateQuotationRequest = quotationRequestApi.update
export const deleteQuotationRequest = quotationRequestApi.delete
export const generateQuotationFromRequest = quotationRequestApi.generateQuotation

export const listQuotationTemplates = quotationTemplateApi.getList
export const getQuotationTemplate = quotationTemplateApi.getById
export const createQuotationTemplate = quotationTemplateApi.create
export const updateQuotationTemplate = quotationTemplateApi.update
export const deleteQuotationTemplate = quotationTemplateApi.delete
export const createQuotationFromTemplate = quotationTemplateApi.createQuotation

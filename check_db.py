#!/usr/bin/env python3
"""
数据库结构检查脚本
分析实际数据库结构并与设计文档对比
"""
import sqlite3
import os

def check_database():
    """检查数据库结构"""
    # 连接数据库
    db_path = 'instance/project.db'
    if not os.path.exists(db_path):
        print('❌ 数据库文件不存在:', db_path)
        return False

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print('🗄️ EMB项目数据库结构分析')
    print('=' * 50)

    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;")
    tables = cursor.fetchall()

    print(f'📊 数据库中共有 {len(tables)} 个表:')
    for table in tables:
        print(f'  - {table[0]}')

    print('\n' + '=' * 50)

    # 分析每个表的结构
    for table in tables:
        table_name = table[0]
        print(f'\n📋 表: {table_name}')
        print('-' * 30)
        
        # 获取表结构
        cursor.execute(f'PRAGMA table_info({table_name});')
        columns = cursor.fetchall()
        
        print('字段信息:')
        for col in columns:
            cid, name, type_, notnull, default, pk = col
            constraints = []
            if pk:
                constraints.append('PRIMARY KEY')
            if notnull:
                constraints.append('NOT NULL')
            if default is not None:
                constraints.append(f'DEFAULT {default}')
            
            constraint_str = ' ' + ' '.join(constraints) if constraints else ''
            print(f'  {name}: {type_}{constraint_str}')
        
        # 获取外键信息
        cursor.execute(f'PRAGMA foreign_key_list({table_name});')
        foreign_keys = cursor.fetchall()
        
        if foreign_keys:
            print('外键约束:')
            for fk in foreign_keys:
                id_, seq, table_ref, from_col, to_col, on_update, on_delete, match = fk
                print(f'  {from_col} -> {table_ref}.{to_col}')
        
        # 获取索引信息
        cursor.execute(f'PRAGMA index_list({table_name});')
        indexes = cursor.fetchall()
        
        if indexes:
            print('索引:')
            for idx in indexes:
                seq, name, unique, origin, partial = idx
                unique_str = ' (UNIQUE)' if unique else ''
                print(f'  {name}{unique_str}')

    conn.close()
    print('\n✅ 数据库结构分析完成')
    return True

if __name__ == '__main__':
    check_database()

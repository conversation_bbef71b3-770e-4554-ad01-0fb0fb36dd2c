#!/usr/bin/env python3
"""
为 order_products 表添加 tax_rate 和 discount 字段的迁移脚本
"""

import sqlite3
import os

def add_tax_discount_fields():
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'project.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(order_products)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 当前 order_products 表字段: {columns}")
        
        # 添加 tax_rate 字段
        if 'tax_rate' not in columns:
            print("➕ 添加 tax_rate 字段...")
            cursor.execute("""
                ALTER TABLE order_products 
                ADD COLUMN tax_rate REAL NOT NULL DEFAULT 13.0
            """)
            print("✅ tax_rate 字段添加成功")
        else:
            print("ℹ️ tax_rate 字段已存在")
        
        # 添加 discount 字段
        if 'discount' not in columns:
            print("➕ 添加 discount 字段...")
            cursor.execute("""
                ALTER TABLE order_products 
                ADD COLUMN discount REAL NOT NULL DEFAULT 0.0
            """)
            print("✅ discount 字段添加成功")
        else:
            print("ℹ️ discount 字段已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证字段添加
        cursor.execute("PRAGMA table_info(order_products)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 更新后 order_products 表字段: {new_columns}")
        
        # 检查现有数据
        cursor.execute("SELECT COUNT(*) FROM order_products")
        count = cursor.fetchone()[0]
        print(f"📊 order_products 表中有 {count} 条记录")
        
        if count > 0:
            # 显示前几条记录的新字段值
            cursor.execute("""
                SELECT id, tax_rate, discount 
                FROM order_products 
                LIMIT 5
            """)
            records = cursor.fetchall()
            print("📝 前5条记录的新字段值:")
            for record in records:
                print(f"  ID {record[0]}: tax_rate={record[1]}, discount={record[2]}")
        
        print("🎉 数据库迁移完成！")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_tax_discount_fields()

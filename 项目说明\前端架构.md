# EMB项目前端架构详解

## 📋 架构概览

EMB前端采用Vue 3 + TypeScript + Element Plus构建的现代化单页应用(SPA)，提供完整的工程物资管理界面。

### 核心特性
- **Vue 3 Composition API**: 现代化的组件开发模式
- **TypeScript**: 类型安全的开发体验
- **Element Plus**: 企业级UI组件库
- **Vite**: 快速的开发构建工具
- **Pinia**: 新一代状态管理
- **Vue Router 4**: 声明式路由管理

## 🏗️ 应用架构

### 应用入口配置
```typescript
// frontend/src/main.ts
import { createApp } from 'vue'
import App from './App.vue'
import { setupRouter } from './router'
import { setupStore } from './stores'
import { initializeApp } from './utils/init'
import ElementPlus from 'element-plus'

async function bootstrap() {
  const app = createApp(App)
  
  // 注册Element Plus
  app.use(ElementPlus)
  
  // 注册图标组件
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  // 设置状态管理
  setupStore(app)
  
  // 设置路由
  setupRouter(app)
  
  // 挂载应用
  app.mount('#app')
  
  // 初始化应用状态
  await initializeApp()
}
```

### 构建配置
```typescript
// frontend/vite.config.ts
export default defineConfig({
  plugins: [vue(), vueDevTools()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5001',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'terser',
    sourcemap: false,
    chunkSizeWarningLimit: 1000
  }
})
```

### 架构特点
1. **模块化设计**: 清晰的模块划分和依赖关系
2. **组件化开发**: 高度可复用的组件设计
3. **类型安全**: TypeScript全面覆盖
4. **开发体验**: 热重载、开发工具集成
5. **生产优化**: 代码分割、压缩优化

## 🗂️ 目录结构

```
frontend/src/
├── api/                    # API接口定义
│   ├── request.ts         # HTTP请求封装
│   ├── index.ts           # API模块总览
│   ├── customer.ts        # 客户管理API
│   ├── product.ts         # 产品管理API
│   ├── order.ts           # 订单管理API
│   └── dashboard.ts       # 工作台API
├── assets/                # 静态资源
│   ├── images/           # 图片资源
│   ├── icons/            # 图标资源
│   └── styles/           # 样式文件
├── components/            # 公共组件
│   ├── common/           # 通用组件
│   ├── forms/            # 表单组件
│   └── charts/           # 图表组件
├── composables/           # 组合式函数
│   ├── useApi.ts         # API调用封装
│   ├── useTable.ts       # 表格功能封装
│   └── useForm.ts        # 表单功能封装
├── layouts/               # 布局组件
│   └── MainLayout.vue    # 主布局
├── router/                # 路由配置
│   └── index.ts          # 路由定义
├── stores/                # 状态管理
│   ├── index.ts          # Store配置
│   └── modules/          # Store模块
│       ├── app.ts        # 应用状态
│       ├── user.ts       # 用户状态
│       └── cache.ts      # 缓存管理
├── types/                 # TypeScript类型定义
│   ├── api.ts            # API类型
│   ├── common.ts         # 通用类型
│   └── business.ts       # 业务类型
├── utils/                 # 工具函数
│   ├── request.ts        # 请求工具
│   ├── format.ts         # 格式化工具
│   └── validation.ts     # 验证工具
├── views/                 # 页面组件
│   ├── Dashboard.vue     # 工作台
│   ├── customers/        # 客户管理
│   ├── products/         # 产品管理
│   ├── quotations/       # 报价管理
│   ├── orders/           # 订单管理
│   ├── delivery/         # 发货管理
│   ├── finance/          # 财务管理
│   └── system/           # 系统设置
├── App.vue               # 根组件
└── main.ts               # 应用入口
```

## 🛣️ 路由架构

### 路由配置
```typescript
// frontend/src/router/index.ts
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: () => import('@/layouts/MainLayout.vue'),
      redirect: '/',
      children: [
        // 工作台
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: {
            title: '工作台',
            icon: 'Monitor',
            requiresAuth: true
          }
        },
        
        // 客户管理
        {
          path: 'customers',
          name: 'Customers',
          component: () => import('@/views/customers/CustomerList.vue'),
          meta: {
            title: '客户列表',
            icon: 'User',
            requiresAuth: true
          }
        },
        
        // 其他路由...
      ]
    }
  ]
})
```

### 路由特性
1. **懒加载**: 路由组件按需加载
2. **嵌套路由**: 支持多层级路由结构
3. **路由守卫**: 权限控制和导航守卫
4. **元信息**: 页面标题、图标、权限等
5. **动态路由**: 支持参数化路由

### 页面路由结构
```
/ (主布局)
├── / (工作台)
├── /customers (客户列表)
├── /customers/new (新增客户)
├── /customers/edit/:id (编辑客户)
├── /customers/:id (客户详情)
├── /products (产品列表)
├── /products/new (新增产品)
├── /products/edit/:id (编辑产品)
├── /products/:id (产品详情)
├── /quotations (报价列表)
├── /quotations/new (新增报价)
├── /quotations/edit/:id (编辑报价)
├── /quotations/:id (报价详情)
├── /orders (订单列表)
├── /orders/new (新增订单)
├── /orders/edit/:id (编辑订单)
├── /orders/:id (订单详情)
├── /delivery (发货管理)
├── /finance (财务管理)
└── /system (系统设置)
```

## 🗃️ 状态管理架构

### Pinia Store配置
```typescript
// frontend/src/stores/index.ts
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

export function setupStore(app: App) {
  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate)
  app.use(pinia)
}
```

### Store模块结构

#### 应用状态管理
```typescript
// frontend/src/stores/modules/app.ts
export const useAppStore = defineStore('app', {
  state: () => ({
    sidebarCollapsed: false,
    theme: 'light',
    language: 'zh-CN',
    loading: false,
    title: 'EMB物资管理系统'
  }),

  getters: {
    isLoading: (state) => state.loading,
    getTitle: (state) => state.title
  },

  actions: {
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    setLoading(loading: boolean) {
      this.loading = loading
    },

    setTitle(title: string) {
      this.title = title
    }
  },

  persist: {
    key: 'emb-app-store',
    storage: localStorage,
    paths: ['sidebarCollapsed', 'theme', 'language']
  }
})
```

#### 用户状态管理
```typescript
// frontend/src/stores/modules/user.ts
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null as UserInfo | null,
    token: '',
    permissions: [] as string[]
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    hasPermission: (state) => (permission: string) =>
      state.permissions.includes(permission)
  },

  actions: {
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
    },

    setToken(token: string) {
      this.token = token
    },

    logout() {
      this.userInfo = null
      this.token = ''
      this.permissions = []
    }
  },

  persist: {
    key: 'emb-user-store',
    storage: localStorage
  }
})
```

#### 缓存管理
```typescript
// frontend/src/stores/modules/cache.ts
export const useCacheStore = defineStore('cache', {
  state: () => ({
    customers: new Map<number, Customer>(),
    products: new Map<number, Product>(),
    categories: [] as ProductCategory[],
    brands: [] as Brand[]
  }),

  getters: {
    getCustomerById: (state) => (id: number) => state.customers.get(id),
    getProductById: (state) => (id: number) => state.products.get(id)
  },

  actions: {
    setCustomers(customers: Customer[]) {
      customers.forEach(customer => {
        this.customers.set(customer.id, customer)
      })
    },

    setProducts(products: Product[]) {
      products.forEach(product => {
        this.products.set(product.id, product)
      })
    },

    clearCache() {
      this.customers.clear()
      this.products.clear()
      this.categories = []
      this.brands = []
    }
  }
})
```

### 状态管理特性
1. **模块化**: 按功能模块划分Store
2. **类型安全**: TypeScript类型支持
3. **持久化**: 自动状态持久化
4. **响应式**: Vue 3响应式系统集成
5. **开发工具**: Vue DevTools支持

## 🔌 API架构

### HTTP请求封装
```typescript
// frontend/src/api/request.ts
import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

class HttpClient {
  private instance: AxiosInstance
  
  constructor() {
    this.instance = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加loading状态
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        const { code, message, data } = response.data
        
        if (code === 200) {
          return data
        } else {
          ElMessage.error(message || '请求失败')
          return Promise.reject(new Error(message))
        }
      },
      (error) => {
        ElMessage.error(error.message || '网络错误')
        return Promise.reject(error)
      }
    )
  }
  
  // HTTP方法封装
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }
  
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }
  
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }
  
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

export const http = new HttpClient()
```

### API模块化
```typescript
// frontend/src/api/customer.ts
import { http } from './request'
import type { Customer, CustomerListParams, CustomerListResponse } from '@/types/customer'

export const customerApi = {
  // 获取客户列表
  getList(params?: CustomerListParams): Promise<CustomerListResponse> {
    return http.get('/v1/customers', { params })
  },
  
  // 获取客户详情
  getDetail(id: number): Promise<Customer> {
    return http.get(`/v1/customers/${id}`)
  },
  
  // 创建客户
  create(data: Partial<Customer>): Promise<Customer> {
    return http.post('/v1/customers', data)
  },
  
  // 更新客户
  update(id: number, data: Partial<Customer>): Promise<Customer> {
    return http.put(`/v1/customers/${id}`, data)
  },
  
  // 删除客户
  delete(id: number): Promise<void> {
    return http.delete(`/v1/customers/${id}`)
  }
}
```

## 🎨 组件架构

### 组件分类
1. **布局组件**: 页面布局和框架
2. **业务组件**: 特定业务功能组件
3. **通用组件**: 可复用的基础组件
4. **表单组件**: 表单相关组件
5. **图表组件**: 数据可视化组件

### 组件设计原则
```vue
<script setup lang="ts">
// 组件props定义
interface Props {
  data?: Customer[]
  loading?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  readonly: false
})

// 组件事件定义
const emit = defineEmits<{
  select: [customer: Customer]
  edit: [id: number]
  delete: [id: number]
}>()

// 响应式数据
const selectedCustomer = ref<Customer | null>(null)

// 计算属性
const hasData = computed(() => props.data.length > 0)

// 方法
const handleSelect = (customer: Customer) => {
  selectedCustomer.value = customer
  emit('select', customer)
}
</script>

<template>
  <div class="customer-list">
    <el-table 
      :data="data" 
      :loading="loading"
      @selection-change="handleSelect"
    >
      <!-- 表格列定义 -->
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.customer-list {
  // 组件样式
}
</style>
```

## 📱 响应式设计

### 断点配置
```scss
// 响应式断点
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}
```

### 自适应布局
1. **栅格系统**: Element Plus栅格布局
2. **弹性布局**: Flexbox和Grid布局
3. **响应式表格**: 表格在小屏幕下的适配
4. **移动端优化**: 触摸友好的交互设计

## 🔧 开发工具集成

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 开发工具
1. **Vue DevTools**: Vue组件调试
2. **Vite DevTools**: 构建过程可视化
3. **ESLint**: 代码质量检查
4. **Prettier**: 代码格式化
5. **TypeScript**: 类型检查

## 🚀 性能优化

### 构建优化
1. **代码分割**: 路由级别的代码分割
2. **Tree Shaking**: 自动移除未使用代码
3. **资源压缩**: JavaScript、CSS、图片压缩
4. **缓存策略**: 长期缓存配置
5. **预加载**: 关键资源预加载

### 运行时优化
1. **虚拟滚动**: 大列表性能优化
2. **图片懒加载**: 图片按需加载
3. **组件缓存**: keep-alive组件缓存
4. **防抖节流**: 用户交互优化
5. **内存管理**: 组件销毁时清理

---

**前端架构版本**: v1.0  
**最后更新**: 2025年1月  
**技术栈**: Vue 3 + TypeScript + Element Plus + Vite  
**维护状态**: 🔄 持续优化中

#!/usr/bin/env python3
"""
应收账款逻辑迁移脚本
从基于发货单/退货单的应收账款迁移到基于对账单的应收账款
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.finance import Statement, StatementReceivable
from datetime import datetime

def migrate_receivables():
    """迁移应收账款逻辑"""
    app = create_app()
    
    with app.app_context():
        print("🔄 开始迁移应收账款逻辑")
        print("=" * 50)
        
        try:
            # 1. 创建新的应收账款表
            print("📋 创建 statement_receivables 表...")
            db.create_all()
            print("✅ 表创建成功")
            
            # 2. 为所有已确认的对账单创建应收账款记录
            print("\n💰 为已确认的对账单创建应收账款记录...")
            
            confirmed_statements = Statement.query.filter(
                Statement.status.in_(['已确认', '部分收款', '已结清'])
            ).all()
            
            created_count = 0
            for statement in confirmed_statements:
                print(f"   处理对账单: {statement.statement_number}")
                
                # 检查是否已存在应收账款记录
                existing = StatementReceivable.query.filter_by(statement_id=statement.id).first()
                if existing:
                    print(f"     ⚠️  应收账款记录已存在，跳过")
                    continue
                
                # 计算应收账款金额 = 调整后金额 - 已付金额
                from decimal import Decimal
                adjusted_total = Decimal(str(statement.adjusted_total_amount or 0))
                paid_amount = Decimal(str(statement.paid_amount or 0))
                receivable_amount = adjusted_total - paid_amount
                
                print(f"     调整后金额: ¥{adjusted_total}")
                print(f"     已付金额: ¥{paid_amount}")
                print(f"     应收金额: ¥{receivable_amount}")
                
                # 如果应收金额大于0，创建应收账款记录
                if receivable_amount > 0:
                    receivable = StatementReceivable(
                        statement_id=statement.id,
                        customer_id=statement.customer_id,
                        amount=receivable_amount,
                        paid_amount=paid_amount,
                        due_date=statement.due_date or statement.statement_date,
                        status='未支付' if paid_amount <= 0 else ('已支付' if paid_amount >= adjusted_total else '部分支付')
                    )
                    
                    if paid_amount > 0:
                        receivable.last_payment_date = datetime.now().date()
                    
                    db.session.add(receivable)
                    created_count += 1
                    print(f"     ✅ 创建应收账款记录")
                else:
                    print(f"     ℹ️  应收金额为0或负数，不创建记录")
            
            # 3. 提交更改
            db.session.commit()
            print(f"\n🎉 迁移完成！")
            print(f"   创建了 {created_count} 条应收账款记录")
            
            # 4. 验证结果
            print("\n📊 验证结果:")
            total_receivables = StatementReceivable.query.count()
            total_amount = db.session.query(db.func.sum(StatementReceivable.amount)).scalar() or 0
            print(f"   总应收账款记录数: {total_receivables}")
            print(f"   总应收金额: ¥{total_amount}")
            
            # 按状态统计
            status_stats = db.session.query(
                StatementReceivable.status,
                db.func.count(StatementReceivable.id),
                db.func.sum(StatementReceivable.amount)
            ).group_by(StatementReceivable.status).all()
            
            print("\n   按状态统计:")
            for status, count, amount in status_stats:
                print(f"     {status}: {count} 条, ¥{amount or 0}")
            
        except Exception as e:
            print(f"❌ 迁移失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    migrate_receivables()

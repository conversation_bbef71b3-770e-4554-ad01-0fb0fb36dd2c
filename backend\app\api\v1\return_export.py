"""
退货单导出API模块
提供退货单的Excel和PDF导出功能
"""

from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
import pandas as pd
from io import BytesIO
import tempfile
import os
from datetime import datetime

from app.models.return_order import ReturnOrder, ReturnOrderItem
from app.models.order import Order
from app.models.customer import Customer
from app.utils.response import success_response, error_response

# 创建命名空间
api = Namespace('return-export', description='退货单导出功能API')

# 列名映射
column_mapping = {
    # 基本信息字段
    'return_number': '退货单号',
    'order_number': '关联订单号',
    'project_name': '项目名称',
    'customer_name': '客户名称',
    'return_date': '退货日期',
    'status': '退货状态',
    'settlement_status': '结清状态',
    'settlement_date': '结清日期',
    'reason': '退货原因',
    'total_amount': '退货总金额',
    'notes': '退货备注',
    'created_at': '创建时间',
    
    # 产品明细字段
    'product_name': '产品名称',
    'product_model': '产品型号',
    'specification_description': '规格描述',
    'quantity': '退货数量',
    'product_unit': '单位',
    'unit_price': '单价',
    'amount': '退货金额',
    'item_reason': '产品退货原因',
    'item_notes': '产品备注'
}

@api.route('/<int:return_order_id>/export')
class ReturnOrderExport(Resource):
    @api.doc('export_return_order')
    def get(self, return_order_id):
        """导出退货单"""
        try:
            # 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 查询退货单数据
            return_order = ReturnOrder.query.get_or_404(return_order_id)
            
            # 字段分类
            basic_fields = ['return_number', 'order_number', 'project_name', 'customer_name', 'return_date', 'status', 'settlement_status', 'settlement_date', 'reason', 'total_amount', 'notes', 'created_at']
            item_fields = ['product_name', 'product_model', 'specification_description', 'quantity', 'product_unit', 'unit_price', 'amount', 'item_reason', 'item_notes']
            
            # 分离请求的字段
            requested_columns = columns or []
            basic_columns = [col for col in requested_columns if col in basic_fields]
            item_columns = [col for col in requested_columns if col in item_fields]
            
            # 基本信息数据
            basic_info = {
                'return_number': return_order.return_number,
                'order_number': return_order.order.order_number if return_order.order else '',
                'project_name': return_order.order.project_name if return_order.order else '',
                'customer_name': return_order.order.customer.name if return_order.order and return_order.order.customer else '',
                'return_date': return_order.return_date.strftime('%Y-%m-%d') if return_order.return_date else '',
                'status': return_order.status or '',
                'settlement_status': return_order.settlement_status or '未结清',
                'settlement_date': return_order.settlement_date.strftime('%Y-%m-%d') if return_order.settlement_date else '',
                'reason': return_order.reason or '',
                'total_amount': f"{float(return_order.total_amount):.2f}" if return_order.total_amount else '0.00',
                'notes': return_order.notes or '',
                'created_at': return_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if return_order.created_at else ''
            }

            # 准备基本信息数据（只有一行）
            basic_data = []
            if basic_columns:
                basic_row = {}
                for col in basic_columns:
                    if col in basic_info:
                        basic_row[col] = basic_info[col]
                basic_data.append(basic_row)

            # 准备产品明细数据
            item_data = []
            for item in return_order.items:
                # 计算退货金额
                unit_price = 0
                if hasattr(item, 'order_product') and item.order_product:
                    unit_price = float(item.order_product.unit_price) if item.order_product.unit_price else 0
                
                amount = unit_price * float(item.quantity) if item.quantity else 0

                item_row = {
                    'product_name': item.product_name or '',
                    'product_model': item.product_model or '',
                    'specification_description': item.specification_description or '',
                    'quantity': str(item.quantity) if item.quantity else '0',
                    'product_unit': item.product_unit or '',
                    'unit_price': f"{unit_price:.2f}",
                    'amount': f"{amount:.2f}",
                    'item_reason': item.reason or '',
                    'item_notes': item.notes or ''
                }

                # 只导出请求的产品字段
                if item_columns:
                    filtered_item_row = {}
                    for col in item_columns:
                        if col in item_row:
                            filtered_item_row[col] = item_row[col]
                    item_data.append(filtered_item_row)
                else:
                    item_data.append(item_row)

            if format_type.lower() == 'xlsx':
                # Excel导出 - 分离布局
                output = BytesIO()

                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    current_row = 0
                    
                    # 写入基本信息（如果有）
                    if basic_data:
                        basic_df = pd.DataFrame(basic_data)
                        if not basic_df.empty:
                            # 重命名基本信息列
                            basic_df = basic_df.rename(columns=column_mapping)
                            # 转置基本信息，使其垂直显示
                            basic_transposed = basic_df.T.reset_index()
                            basic_transposed.columns = ['项目', '内容']
                            basic_transposed.to_excel(writer, sheet_name='退货单详情', index=False, startrow=current_row)
                            current_row += len(basic_transposed) + 3  # 基本信息行数 + 空行
                    
                    # 写入产品明细（如果有）
                    if item_data:
                        item_df = pd.DataFrame(item_data)
                        if not item_df.empty:
                            # 重命名产品明细列
                            item_df = item_df.rename(columns=column_mapping)
                            
                            # 添加产品明细标题
                            if basic_data:  # 如果有基本信息，添加产品明细标题
                                title_df = pd.DataFrame([['产品明细']], columns=[''])
                                title_df.to_excel(writer, sheet_name='退货单详情', index=False, header=False, startrow=current_row)
                                current_row += 2
                            
                            # 写入产品明细表格
                            item_df.to_excel(writer, sheet_name='退货单详情', index=False, startrow=current_row)
                    
                    # 如果没有任何数据，创建空表格
                    if not basic_data and not item_data:
                        empty_df = pd.DataFrame([['暂无数据']], columns=['提示'])
                        empty_df.to_excel(writer, sheet_name='退货单详情', index=False)

                output.seek(0)
                
                # 生成文件名
                filename = f"退货单_{return_order.return_number}_{datetime.now().strftime('%Y%m%d')}.xlsx"
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
                temp_file.write(output.getvalue())
                temp_file.close()
                
                return send_file(
                    temp_file.name,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

            elif format_type.lower() == 'pdf':
                # PDF导出 - 分离布局
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib import colors
                from reportlab.lib.units import inch
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                import platform

                # 注册中文字体 - 使用系统字体
                font_name = 'Helvetica'  # 默认字体
                
                try:
                    system = platform.system()
                    if system == 'Windows':
                        # Windows系统字体路径
                        font_paths = [
                            'C:/Windows/Fonts/simhei.ttf',  # 黑体
                            'C:/Windows/Fonts/simsun.ttc',  # 宋体
                            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
                            'C:/Windows/Fonts/simkai.ttf',  # 楷体
                        ]
                    elif system == 'Darwin':  # macOS
                        font_paths = [
                            '/System/Library/Fonts/PingFang.ttc',
                            '/System/Library/Fonts/STHeiti Light.ttc',
                        ]
                    else:  # Linux
                        font_paths = [
                            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                        ]
                    
                    # 尝试加载中文字体
                    for font_path in font_paths:
                        if os.path.exists(font_path):
                            try:
                                pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                                font_name = 'ChineseFont'
                                print(f"成功加载中文字体: {font_path}")
                                break
                            except Exception as e:
                                print(f"加载字体失败 {font_path}: {str(e)}")
                                continue
                    
                    # 如果没有找到合适的字体，尝试使用reportlab的内置中文字体
                    if font_name == 'Helvetica':
                        try:
                            from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                            font_name = 'STSong-Light'
                            print("使用内置中文字体: STSong-Light")
                        except Exception as e:
                            print(f"无法加载内置中文字体: {str(e)}")
                            font_name = 'Helvetica'
                                    
                except Exception as e:
                    print(f"字体加载异常: {str(e)}")
                    font_name = 'Helvetica'

                # 创建临时PDF文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
                temp_file.close()

                # 创建PDF文档
                doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
                
                # 创建样式
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=font_name,
                    fontSize=16,
                    alignment=1,  # 居中
                    spaceAfter=20
                )
                
                section_style = ParagraphStyle(
                    'SectionTitle',
                    parent=styles['Heading2'],
                    fontName=font_name,
                    fontSize=12,
                    alignment=0,  # 左对齐
                    spaceAfter=10,
                    spaceBefore=20
                )

                story = []

                # 添加标题
                title = Paragraph(f"退货单详情 - {return_order.return_number}", title_style)
                story.append(title)
                story.append(Spacer(1, 20))

                # 添加基本信息部分
                if basic_data:
                    basic_section = Paragraph("基本信息", section_style)
                    story.append(basic_section)
                    
                    # 创建基本信息表格（两列：项目名称和内容）
                    basic_table_data = []
                    if include_header:
                        basic_table_data.append(['项目', '内容'])
                    
                    for item in basic_data:
                        for key, value in item.items():
                            display_key = column_mapping.get(key, key)
                            basic_table_data.append([display_key, str(value)])
                    
                    if basic_table_data:
                        basic_table = Table(basic_table_data, colWidths=[2*inch, 4*inch])
                        basic_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey) if include_header else ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 10),
                            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                            ('TOPPADDING', (0, 0), (-1, -1), 6),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(basic_table)
                        story.append(Spacer(1, 20))

                # 添加产品明细部分
                if item_data:
                    item_section = Paragraph("产品明细", section_style)
                    story.append(item_section)
                    
                    # 创建产品明细表格
                    item_df = pd.DataFrame(item_data)
                    if not item_df.empty:
                        item_df = item_df.rename(columns=column_mapping)
                        
                        # 转换为表格数据
                        item_table_data = [item_df.columns.tolist()] if include_header else []
                        item_table_data.extend(item_df.values.tolist())

                        # 创建产品明细表格
                        item_table = Table(item_table_data)
                        item_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))

                        story.append(item_table)

                # 构建PDF
                doc.build(story)
                
                # 生成文件名
                filename = f"退货单_{return_order.return_number}_{datetime.now().strftime('%Y%m%d')}.pdf"
                
                return send_file(
                    temp_file.name,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/pdf'
                )

            else:
                return error_response("不支持的导出格式", code=400)

        except Exception as e:
            current_app.logger.error(f"导出退货单失败: {str(e)}")
            return error_response(f"导出失败: {str(e)}", code=500)

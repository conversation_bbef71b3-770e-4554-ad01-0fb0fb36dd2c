"""
产品相关的序列化模式
基于原项目的Schema定义，确保与现有数据库结构100%兼容
"""
from marshmallow import Schema, fields, validate, validates, ValidationError
from typing import Dict, List, Optional, Any


class ProductAttributeSchema(Schema):
    """产品自定义属性序列化模式"""
    id = fields.Int(dump_only=True)
    product_id = fields.Int(load_only=True)
    attribute_name = fields.Str(required=True, validate=validate.Length(max=100))
    attribute_value = fields.Str(required=True, validate=validate.Length(max=255))
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class ProductSpecificationSchema(Schema):
    """产品规格序列化模式"""
    id = fields.Int(dump_only=True)
    product_id = fields.Int(load_only=True)
    specification = fields.Str(required=True, validate=validate.Length(max=100))
    cost_price = fields.Decimal(required=True, places=2, as_string=True)
    suggested_price = fields.Decimal(required=True, places=2, as_string=True)
    min_price = fields.Decimal(allow_none=True, places=2, as_string=True)
    max_price = fields.Decimal(allow_none=True, places=2, as_string=True)
    tax_rate = fields.Float(load_default=13.0)
    is_default = fields.Bool(load_default=False)
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 计算字段
    taxed_price = fields.Method("get_taxed_price", dump_only=True)
    untaxed_price = fields.Method("get_untaxed_price", dump_only=True)
    
    def get_taxed_price(self, obj):
        """获取含税价格"""
        if hasattr(obj, 'get_taxed_price'):
            return obj.get_taxed_price()
        # 如果模型没有方法，手动计算
        return float(obj.suggested_price) * (1 + obj.tax_rate / 100)
    
    def get_untaxed_price(self, obj):
        """获取不含税价格"""
        if hasattr(obj, 'get_untaxed_price'):
            return obj.get_untaxed_price()
        # 如果模型没有方法，手动计算
        return float(obj.suggested_price) / (1 + obj.tax_rate / 100)


class ProductImageSchema(Schema):
    """产品图片序列化模式"""
    id = fields.Int(dump_only=True)
    product_id = fields.Int(load_only=True)
    url = fields.Str(required=True, validate=validate.Length(max=255))
    file_name = fields.Str(allow_none=True, validate=validate.Length(max=100))
    alt_text = fields.Str(allow_none=True, validate=validate.Length(max=255))
    is_main = fields.Bool(load_default=False)
    sort_order = fields.Int(load_default=0)
    file_size = fields.Int(allow_none=True, validate=validate.Range(min=0))
    width = fields.Int(allow_none=True, validate=validate.Range(min=0))
    height = fields.Int(allow_none=True, validate=validate.Range(min=0))
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class ProductCategorySchema(Schema):
    """产品类别序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    parent_id = fields.Int(allow_none=True)
    level = fields.Int(dump_only=True)
    sort_order = fields.Int(load_default=0)
    description = fields.Str(allow_none=True)
    notes = fields.Str(allow_none=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联字段
    children = fields.List(fields.Nested(lambda: ProductCategorySchema(exclude=("children",))), dump_only=True)
    
    # 计算字段
    full_category_path = fields.Method("get_full_path", dump_only=True)
    
    def get_full_path(self, obj):
        """获取完整分类路径"""
        if hasattr(obj, 'get_full_category_path'):
            return obj.get_full_category_path()
        return obj.name


class BrandSchema(Schema):
    """品牌序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True, validate=validate.Length(min=1, max=100))
    logo_url = fields.Str(allow_none=True, validate=validate.Length(max=255))
    description = fields.Str(allow_none=True)
    website = fields.Str(allow_none=True, validate=validate.Length(max=255))
    sort_order = fields.Int(load_default=0)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)


class BrandSimpleSchema(Schema):
    """简化版品牌序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(dump_only=True)


class ProductSchema(Schema):
    """产品序列化模式"""
    id = fields.Int(dump_only=True)
    name = fields.Str(required=True, validate=validate.Length(max=100))
    model = fields.Str(required=True, validate=validate.Length(max=50))
    unit = fields.Str(required=True, validate=validate.Length(max=20))
    category_id = fields.Int(required=True)
    brand_id = fields.Int(allow_none=True)
    image = fields.Str(allow_none=True, validate=validate.Length(max=255))
    description = fields.Str(allow_none=True)
    notes = fields.Str(allow_none=True)
    status = fields.Str(load_default='正常', validate=validate.OneOf(['正常', '禁用']))
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    
    # 关联数据
    category = fields.Nested(ProductCategorySchema, dump_only=True)
    brand = fields.Nested(BrandSimpleSchema, dump_only=True)
    category_name = fields.Str(dump_only=True, attribute="category.name")
    brand_name = fields.Str(dump_only=True, attribute="brand.name")
    specifications = fields.List(fields.Nested(ProductSpecificationSchema), required=False)
    attributes = fields.List(fields.Nested(ProductAttributeSchema), required=False)
    images = fields.List(fields.Nested(ProductImageSchema), required=False)
    
    # 计算字段
    price_range = fields.Method("get_price_range", dump_only=True)
    main_image = fields.Method("get_main_image", dump_only=True)
    
    def get_price_range(self, obj):
        """获取价格范围"""
        try:
            if hasattr(obj, 'get_price_range'):
                result = obj.get_price_range()
                if result:
                    return result

            # 如果模型没有方法，从规格中计算
            if hasattr(obj, 'specifications') and obj.specifications:
                prices = [float(spec.suggested_price) for spec in obj.specifications if spec.suggested_price]
                if prices:
                    min_price = min(prices)
                    max_price = max(prices)
                    if min_price == max_price:
                        return f"¥{min_price:.2f}"
                    else:
                        return f"¥{min_price:.2f} - ¥{max_price:.2f}"
        except Exception as e:
            # 记录错误但不中断序列化
            import logging
            logging.warning(f"Error calculating price range: {e}")
        return "暂无价格"
    
    def get_main_image(self, obj):
        """获取主图"""
        try:
            if hasattr(obj, 'images') and obj.images:
                for img in obj.images:
                    if img.is_main:
                        return img.url
                # 如果没有主图，返回第一张图
                return obj.images[0].url if obj.images else None
            return getattr(obj, 'image', None)  # 向后兼容
        except Exception:
            return getattr(obj, 'image', None)


class ProductSimpleSchema(Schema):
    """简化版产品序列化模式，不包含关联数据"""
    id = fields.Int(dump_only=True)
    name = fields.Str(dump_only=True)
    model = fields.Str(dump_only=True)
    unit = fields.Str(dump_only=True)
    category_id = fields.Int(dump_only=True)
    brand_id = fields.Int(dump_only=True)
    image = fields.Str(dump_only=True)
    status = fields.Str(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    
    # 关联数据
    category_name = fields.Str(dump_only=True, attribute="category.name")
    brand_name = fields.Str(dump_only=True, attribute="brand.name")
    
    # 计算字段
    main_image = fields.Method("get_main_image", dump_only=True)
    
    def get_main_image(self, obj):
        """获取主图"""
        try:
            if hasattr(obj, 'images') and obj.images:
                for img in obj.images:
                    if img.is_main:
                        return img.url
                return obj.images[0].url if obj.images else None
            return getattr(obj, 'image', None)
        except Exception:
            return getattr(obj, 'image', None)

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API代理测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>🔗 EMB API代理测试</h1>
    
    <div class="test-section">
        <h3>1. 测试GET请求 - 获取客户列表</h3>
        <button onclick="testGetCustomers()">测试GET /api/v1/customers</button>
        <div id="get-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试POST请求 - 创建客户</h3>
        <button onclick="testCreateCustomer()">测试POST /api/v1/customers</button>
        <div id="post-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试API文档访问</h3>
        <button onclick="testApiDocs()">测试GET /api/v1/docs</button>
        <div id="docs-result" class="result"></div>
    </div>

    <script>
        async function testGetCustomers() {
            const resultDiv = document.getElementById('get-result');
            resultDiv.innerHTML = '🔄 正在测试...';
            
            try {
                const response = await fetch('/api/v1/customers');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ GET请求成功!</strong><br>
                        状态码: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ GET请求失败</strong><br>
                        状态码: ${response.status}<br>
                        错误信息: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 网络错误</strong><br>
                    错误信息: ${error.message}
                `;
            }
        }

        async function testCreateCustomer() {
            const resultDiv = document.getElementById('post-result');
            resultDiv.innerHTML = '🔄 正在测试...';
            
            const customerData = {
                name: "API代理测试客户",
                contact: "张三",
                phone: "15554412214",
                email: "<EMAIL>",
                address: "北京市朝阳区",
                tax_id: "*********",
                level: "normal",
                status: "active",
                notes: "通过API代理创建的测试客户",
                source: "api_proxy_test"
            };
            
            try {
                const response = await fetch('/api/v1/customers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(customerData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ POST请求成功!</strong><br>
                        状态码: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ POST请求失败</strong><br>
                        状态码: ${response.status}<br>
                        错误信息: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 网络错误</strong><br>
                    错误信息: ${error.message}
                `;
            }
        }

        async function testApiDocs() {
            const resultDiv = document.getElementById('docs-result');
            resultDiv.innerHTML = '🔄 正在测试...';
            
            try {
                const response = await fetch('/api/v1/');
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ API根路径访问成功!</strong><br>
                        状态码: ${response.status}<br>
                        Content-Type: ${response.headers.get('content-type')}<br>
                        <a href="/api/v1/docs/" target="_blank">🔗 打开API文档</a>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>❌ API访问失败</strong><br>
                        状态码: ${response.status}
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>❌ 网络错误</strong><br>
                    错误信息: ${error.message}
                `;
            }
        }

        // 页面加载时自动测试GET请求
        window.onload = function() {
            console.log('🚀 API代理测试页面已加载');
            console.log('前端服务器: http://localhost:3000');
            console.log('API代理: http://localhost:3000/api/* -> http://localhost:5001/api/*');
        };
    </script>
</body>
</html>

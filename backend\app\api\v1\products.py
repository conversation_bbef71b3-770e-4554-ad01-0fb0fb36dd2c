"""
产品管理API
提供产品信息、产品类别和产品属性的CRUD操作
基于原项目API实现，确保与现有数据库结构100%兼容
"""
from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from sqlalchemy import or_, desc
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
from io import BytesIO
from werkzeug.datastructures import FileStorage
from datetime import datetime
import os

from app.models.product import Product, ProductCategory, ProductAttribute, ProductSpecification, ProductImage
from app.models.brand import Brand
from app.schemas.product import (
    ProductSchema, 
    ProductSimpleSchema, 
    ProductCategorySchema, 
    ProductAttributeSchema, 
    ProductSpecificationSchema,
    ProductImageSchema,
    BrandSchema,
    BrandSimpleSchema
)
from app.utils.response import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response,
    not_found_response
)
from app.utils.pagination import PaginationHelper
from app.utils.exceptions import ValidationError as CustomValidationError, NotFoundError
from app.utils.schema_to_restx import create_input_model, create_output_model
from app import db

# 创建命名空间
api = Namespace('products', description='产品管理API')

def make_response(response_func, *args, **kwargs):
    """辅助函数：将响应函数的返回值转换为Flask-RESTX兼容格式"""
    response_data, status_code = response_func(*args, **kwargs)
    return response_data, status_code

# 自动从Marshmallow Schema生成API模型
product_input_model = create_input_model(api, ProductSchema, 'ProductInput')
product_output_model = create_output_model(api, ProductSchema, 'ProductOutput')

# 为了向后兼容，保留原来的product_model名称
product_model = product_input_model

# 自动生成其他模型
category_model = create_input_model(api, ProductCategorySchema, 'ProductCategory')

specification_model = create_input_model(api, ProductSpecificationSchema, 'ProductSpecification')
attribute_model = create_input_model(api, ProductAttributeSchema, 'ProductAttribute')
brand_model = create_input_model(api, BrandSchema, 'Brand')


@api.route('')
class ProductList(Resource):
    @api.doc('get_products')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    @api.param('name', '产品名称搜索')
    @api.param('model', '产品型号搜索')
    @api.param('search', '综合搜索（同时搜索名称和型号）')
    @api.param('category_id', '产品分类ID', type='integer')
    @api.param('brand_id', '品牌ID', type='integer')
    @api.param('status', '产品状态')
    @api.param('with_specifications', '是否包含规格信息', type='boolean')
    def get(self):
        """获取产品列表"""
        try:
            # 查询参数处理
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            name = request.args.get('name', '')
            model = request.args.get('model', '')
            search = request.args.get('search', '')  # 新增综合搜索参数
            category_id = request.args.get('category_id', type=int)
            brand_id = request.args.get('brand_id', type=int)
            status = request.args.get('status', '')
            with_specifications = request.args.get('with_specifications', '').lower() in ('true', '1', 'yes')
            
            # 构建查询
            query = Product.query
            
            if with_specifications:
                query = query.options(selectinload(Product.specifications))

            # 应用过滤条件
            if search:
                # 综合搜索：同时搜索名称和型号（OR条件）
                query = query.filter(
                    db.or_(
                        Product.name.ilike(f'%{search}%'),
                        Product.model.ilike(f'%{search}%')
                    )
                )
            else:
                # 分别搜索名称和型号（AND条件）
                if name:
                    query = query.filter(Product.name.ilike(f'%{name}%'))
                if model:
                    query = query.filter(Product.model.ilike(f'%{model}%'))

            if category_id:
                query = query.filter(Product.category_id == category_id)
            if brand_id:
                query = query.filter(Product.brand_id == brand_id)
            if status:
                query = query.filter(Product.status == status)
            
            # 排序
            query = query.order_by(Product.created_at.desc())
            
            # 选择Schema
            if with_specifications:
                serializer_func = lambda item: ProductSchema().dump(item)
            else:
                serializer_func = lambda item: ProductSimpleSchema().dump(item)
            
            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query,
                serializer_func=serializer_func,
                page=page,
                per_page=per_page,
                message="获取产品列表成功"
            )
            
        except Exception as e:
            current_app.logger.error(f"获取产品列表失败: {str(e)}")
            return make_response(error_response, f"获取产品列表失败: {str(e)}")

    @api.doc('create_product',
             body=product_model,
             responses={
                 201: 'Success',
                 400: 'Validation Error',
                 500: 'Internal Server Error'
             })
    def post(self):
        """创建新产品"""
        try:
            data = request.get_json() or {}
            
            # 分离嵌套数据
            specifications_data = data.pop('specifications', [])
            attributes_data = data.pop('attributes', [])
            images_data = data.pop('images', [])
            files_to_delete = data.pop('files_to_delete', None)
            
            # 数据验证
            product_schema = ProductSchema(exclude=(
                'id', 'specifications', 'attributes', 'images', 'category', 'brand',
                'category_name', 'brand_name', 'price_range', 'main_image', 'created_at', 'updated_at'
            ))
            validated_data = product_schema.load(data)

            # 检查产品类别是否存在
            if not ProductCategory.query.get(validated_data['category_id']):
                return make_response(error_response, "产品类别不存在", errors={"category_id": "指定的产品类别不存在"})

            # 检查品牌是否存在（如果提供了brand_id）
            if validated_data.get('brand_id') and not Brand.query.get(validated_data['brand_id']):
                return make_response(error_response, "品牌不存在", errors={"brand_id": "指定的品牌不存在"})

            # 检查产品名称和型号组合是否唯一
            if Product.query.filter_by(name=validated_data['name'], model=validated_data['model']).first():
                return make_response(error_response, "产品已存在", errors={"name": "此产品名称和型号组合已存在"})

            # 验证规格：至少要有一个规格
            if not specifications_data or len(specifications_data) == 0:
                return make_response(error_response, "产品至少需要一个规格", errors={"specifications": "请至少添加一个产品规格"})

            # 创建产品对象
            product = Product(**validated_data)
            db.session.add(product)
            db.session.flush()  # 获取产品ID

            # 创建规格信息
            if specifications_data:
                spec_schema = ProductSpecificationSchema(many=True, exclude=('id', 'product_id'))
                validated_specs = spec_schema.load(specifications_data)
                
                for spec_data in validated_specs:
                    spec_data['product_id'] = product.id
                    specification = ProductSpecification(**spec_data)
                    db.session.add(specification)

            # 创建属性信息
            if attributes_data:
                attr_schema = ProductAttributeSchema(many=True, exclude=('id', 'product_id'))
                validated_attrs = attr_schema.load(attributes_data)
                
                for attr_data in validated_attrs:
                    attr_data['product_id'] = product.id
                    attribute = ProductAttribute(**attr_data)
                    db.session.add(attribute)

            # 创建图片信息
            if images_data:
                image_schema = ProductImageSchema(many=True, exclude=('id', 'product_id'))
                validated_images = image_schema.load(images_data)
                
                # 如果没有指定主图，则第一张图片默认为主图
                has_main = any(img.get('is_main', False) for img in validated_images)
                
                for i, img_data in enumerate(validated_images):
                    img_data['product_id'] = product.id
                    
                    # 第一张图设为主图（如果没有指定主图）
                    if not has_main and i == 0:
                        img_data['is_main'] = True
                        # 同时更新产品的image字段（向后兼容）
                        product.image = img_data['url']
                    
                    product_image = ProductImage(**img_data)
                    db.session.add(product_image)

            # 处理需要删除的临时文件
            if files_to_delete:
                current_app.logger.info(f"删除临时文件: {files_to_delete}")
                for file_url in files_to_delete:
                    delete_product_image_file(file_url)

            db.session.commit()

            # 返回创建的产品信息
            created_product = Product.query.get(product.id)
            response_data = ProductSchema().dump(created_product)
            return make_response(success_response, response_data, "产品创建成功", 201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建产品失败: {str(e)}")


@api.route('/<int:product_id>')
class ProductDetail(Resource):
    @api.doc('get_product')
    def get(self, product_id):
        """获取产品详情"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            product_data = ProductSchema().dump(product)
            return make_response(success_response, product_data, "获取产品详情成功")

        except Exception as e:
            current_app.logger.error(f"获取产品详情失败: {str(e)}")
            return make_response(error_response, f"获取产品详情失败: {str(e)}")

    @api.doc('update_product',
             body=product_model,
             responses={
                 200: 'Success',
                 400: 'Validation Error',
                 404: 'Product Not Found',
                 500: 'Internal Server Error'
             })
    def put(self, product_id):
        """更新产品信息"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            data = request.get_json() or {}

            # 分离规格、属性、图片数据和需要删除的文件
            specifications_data = data.pop('specifications', None)
            attributes_data = data.pop('attributes', None)
            images_data = data.pop('images', None)
            files_to_delete = data.pop('files_to_delete', None)

            # 验证并更新产品基本信息
            product_schema = ProductSchema(exclude=(
                'specifications', 'attributes', 'images', 'category', 'brand',
                'category_name', 'brand_name', 'price_range', 'main_image'
            ))
            validated_product_data = product_schema.load(data, partial=True)

            # 检查类别和品牌
            if 'category_id' in validated_product_data:
                if not ProductCategory.query.get(validated_product_data['category_id']):
                    return make_response(error_response, "产品类别不存在", errors={"category_id": "指定的产品类别不存在"})

            if 'brand_id' in validated_product_data and validated_product_data['brand_id']:
                if not Brand.query.get(validated_product_data['brand_id']):
                    return make_response(error_response, "品牌不存在", errors={"brand_id": "指定的品牌不存在"})

            # 检查唯一性约束
            if ('name' in validated_product_data or 'model' in validated_product_data):
                check_name = validated_product_data.get('name', product.name)
                check_model = validated_product_data.get('model', product.model)
                if (check_name != product.name or check_model != product.model):
                    if Product.query.filter(
                        Product.id != product_id,
                        Product.name == check_name,
                        Product.model == check_model
                    ).first():
                        return make_response(error_response, "产品已存在", errors={"name": "此产品名称和型号组合已存在"})

            # 更新基本信息
            for key, value in validated_product_data.items():
                setattr(product, key, value)

            # 处理规格信息
            if specifications_data is not None:
                # 验证规格：至少要有一个规格
                if len(specifications_data) == 0:
                    return make_response(error_response, "产品至少需要一个规格", errors={"specifications": "请至少添加一个产品规格"})

                spec_schema = ProductSpecificationSchema(many=True)
                validated_specs = spec_schema.load(specifications_data)

                current_spec_ids = {spec.id for spec in product.specifications}
                incoming_spec_ids = {spec_data.get('id') for spec_data in validated_specs if spec_data.get('id')}

                # 删除不再存在的规格
                ids_to_delete = current_spec_ids - incoming_spec_ids
                if ids_to_delete:
                    ProductSpecification.query.filter(
                        ProductSpecification.product_id == product_id,
                        ProductSpecification.id.in_(ids_to_delete)
                    ).delete(synchronize_session=False)

                # 更新或添加规格
                for spec_data in validated_specs:
                    spec_id = spec_data.get('id')
                    if spec_id:
                        # 更新现有规格
                        spec_to_update = ProductSpecification.query.filter_by(id=spec_id, product_id=product_id).first()
                        if spec_to_update:
                            for k, v in spec_data.items():
                                if k != 'id':
                                    setattr(spec_to_update, k, v)
                    else:
                        # 添加新规格
                        spec_data['product_id'] = product_id
                        new_spec = ProductSpecification(**spec_data)
                        db.session.add(new_spec)

            # 处理属性信息
            if attributes_data is not None:
                attr_schema = ProductAttributeSchema(many=True)
                validated_attrs = attr_schema.load(attributes_data)

                current_attr_ids = {attr.id for attr in product.attributes}
                incoming_attr_ids = {attr_data.get('id') for attr_data in validated_attrs if attr_data.get('id')}

                # 删除不再存在的属性
                ids_to_delete = current_attr_ids - incoming_attr_ids
                if ids_to_delete:
                    ProductAttribute.query.filter(
                        ProductAttribute.product_id == product_id,
                        ProductAttribute.id.in_(ids_to_delete)
                    ).delete(synchronize_session=False)

                # 更新或添加属性
                for attr_data in validated_attrs:
                    attr_id = attr_data.get('id')
                    if attr_id:
                        # 更新现有属性
                        attr_to_update = ProductAttribute.query.filter_by(id=attr_id, product_id=product_id).first()
                        if attr_to_update:
                            for k, v in attr_data.items():
                                if k != 'id' and k != 'product_id':
                                    setattr(attr_to_update, k, v)
                    else:
                        # 添加新属性
                        attr_data['product_id'] = product_id
                        new_attr = ProductAttribute(**attr_data)
                        db.session.add(new_attr)

            # 处理图片信息
            if images_data is not None:
                current_app.logger.info(f"处理图片信息，接收到的数据: {images_data}")
                image_schema = ProductImageSchema(many=True)
                validated_images = image_schema.load(images_data)

                # 获取当前图片URL集合和新图片URL集合
                current_image_urls = {img.url for img in product.images}
                incoming_image_urls = {img_data.get('url') for img_data in validated_images if img_data.get('url')}

                current_app.logger.info(f"当前图片URLs: {current_image_urls}")
                current_app.logger.info(f"新提交的图片URLs: {incoming_image_urls}")

                # 找出要删除的图片URL
                urls_to_delete = current_image_urls - incoming_image_urls
                current_app.logger.info(f"要删除的图片URLs: {urls_to_delete}")

                if urls_to_delete:
                    # 先获取要删除的图片记录，用于删除文件
                    images_to_delete = ProductImage.query.filter(
                        ProductImage.product_id == product_id,
                        ProductImage.url.in_(urls_to_delete)
                    ).all()

                    # 删除对应的图片文件
                    for img in images_to_delete:
                        current_app.logger.info(f"删除图片文件: {img.url}")
                        delete_product_image_file(img.url)

                    # 删除数据库记录
                    ProductImage.query.filter(
                        ProductImage.product_id == product_id,
                        ProductImage.url.in_(urls_to_delete)
                    ).delete(synchronize_session=False)

                # 检查是否有新的主图
                has_main_in_update = any(img_data.get('is_main', False) for img_data in validated_images)

                # 更新或添加图片
                for img_data in validated_images:
                    img_url = img_data.get('url')
                    if img_url:
                        # 检查是否是现有图片（基于URL）
                        existing_img = ProductImage.query.filter_by(url=img_url, product_id=product_id).first()
                        if existing_img:
                            # 更新现有图片
                            # 如果将此图片设为主图，则更新其他图片
                            if img_data.get('is_main', False) and not existing_img.is_main:
                                ProductImage.query.filter(
                                    ProductImage.product_id == product_id,
                                    ProductImage.url != img_url
                                ).update({'is_main': False})
                                # 更新产品的主图字段
                                product.image = img_url

                            # 更新字段
                            for k, v in img_data.items():
                                if k != 'product_id':
                                    setattr(existing_img, k, v)
                        else:
                            # 添加新图片
                            img_data['product_id'] = product_id

                            # 如果没有主图，且这是第一张新图，则设为主图
                            if not has_main_in_update and not ProductImage.query.filter_by(product_id=product_id, is_main=True).first():
                                img_data['is_main'] = True
                                product.image = img_data['url']

                            # 如果此图片被指定为主图，则更新其他图片
                            if img_data.get('is_main', False):
                                ProductImage.query.filter_by(product_id=product_id).update({'is_main': False})
                                product.image = img_data['url']

                            new_img = ProductImage(**img_data)
                            db.session.add(new_img)

                # 确保至少有一张主图
                if validated_images:
                    if not ProductImage.query.filter_by(product_id=product_id, is_main=True).first():
                        first_image = ProductImage.query.filter_by(product_id=product_id).order_by(ProductImage.id).first()
                        if first_image:
                            first_image.is_main = True
                            product.image = first_image.url

            # 处理需要删除的临时文件
            if files_to_delete:
                current_app.logger.info(f"删除临时文件: {files_to_delete}")
                for file_url in files_to_delete:
                    delete_product_image_file(file_url)

            db.session.commit()

            # 返回更新后的产品
            updated_data = ProductSchema().dump(product)
            return make_response(success_response, updated_data, "产品更新成功")

        except ValidationError as e:
            db.session.rollback()
            current_app.logger.error(f"产品更新验证失败: {e.messages}")
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新产品失败: {str(e)}")

    @api.doc('delete_product')
    def delete(self, product_id):
        """删除产品"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            # 删除产品关联的图片文件
            for image in product.images:
                delete_product_image_file(image.url)

            db.session.delete(product)
            db.session.commit()
            return make_response(success_response, message="产品删除成功")

        except Exception as e:
            current_app.logger.error(f"删除产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除产品失败: {str(e)}")


# 产品分类相关API
@api.route('/categories')
class ProductCategoryList(Resource):
    @api.doc('get_product_categories')
    @api.param('parent_id', '父分类ID')
    @api.param('tree', '是否返回树状结构', type='boolean')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=10)
    def get(self):
        """获取产品分类列表（支持树状结构或按父ID查询）"""
        try:
            parent_id_str = request.args.get('parent_id')
            get_tree = request.args.get('tree', type=bool, default=False)
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            if get_tree:
                # 获取所有分类并构建树状结构
                all_categories = ProductCategory.query.order_by(ProductCategory.sort_order).all()

                # 构建ID到对象的映射
                category_map = {cat.id: cat for cat in all_categories}
                # 构建父ID到子列表的映射
                children_map = {}
                for cat in all_categories:
                    if cat.parent_id:
                        if cat.parent_id not in children_map:
                            children_map[cat.parent_id] = []
                        children_map[cat.parent_id].append(cat)

                # 构建树
                root_categories = []
                for cat in all_categories:
                    cat.children = children_map.get(cat.id, [])  # 附加子节点
                    if cat.parent_id is None or cat.parent_id == 0:
                        root_categories.append(cat)

                category_schema = ProductCategorySchema(many=True)
                categories_data = category_schema.dump(root_categories)
                response_data = {
                    "items": categories_data,
                    "page_info": None  # 不分页
                }
                return make_response(success_response, response_data, "获取产品分类树成功")

            else:
                # 按父ID查询（如果提供了parent_id）或分页查询顶层分类
                query = ProductCategory.query

                if parent_id_str is not None:
                    if parent_id_str.lower() == 'null' or parent_id_str == '0':
                        # 查询顶级分类
                        query = query.filter(or_(ProductCategory.parent_id.is_(None), ProductCategory.parent_id == 0))
                    else:
                        try:
                            parent_id = int(parent_id_str)
                            query = query.filter(ProductCategory.parent_id == parent_id)
                        except ValueError:
                            return make_response(error_response, "无效的parent_id", code=400)

                # 使用分页助手
                return make_response(
                    PaginationHelper.paginate_and_response,
                    query=query.order_by(ProductCategory.level, ProductCategory.sort_order, ProductCategory.name),
                    serializer_func=lambda item: ProductCategorySchema(exclude=('children',)).dump(item),
                    page=page,
                    per_page=per_page,
                    message="获取产品分类列表成功"
                )

        except Exception as e:
            current_app.logger.error(f"获取产品分类失败: {str(e)}")
            return make_response(error_response, f"获取产品分类失败: {str(e)}")

    @api.doc('create_product_category')
    def post(self):
        """创建新产品分类"""
        try:
            data = request.get_json() or {}
            category_schema = ProductCategorySchema(exclude=(
                'id', 'level', 'children', 'full_category_path', 'created_at', 'updated_at'
            ))

            validated_data = category_schema.load(data)

            # 检查名称是否重复（在同一父级下）
            existing = ProductCategory.query.filter_by(
                name=validated_data['name'],
                parent_id=validated_data.get('parent_id')
            ).first()
            if existing:
                return make_response(error_response, f"名称 '{validated_data['name']}' 在此级别下已存在", code=400)

            # 计算level
            parent_level = 0
            if validated_data.get('parent_id'):
                parent = ProductCategory.query.get(validated_data['parent_id'])
                if not parent:
                    return make_response(error_response, "父类别不存在", code=400)
                parent_level = parent.level

            validated_data['level'] = parent_level + 1

            category = ProductCategory(**validated_data)
            db.session.add(category)
            db.session.commit()

            # 返回完整数据
            return make_response(success_response, ProductCategorySchema().dump(category), "产品类别创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"创建产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"创建产品类别失败: {str(e)}")


@api.route('/categories/<int:category_id>')
class ProductCategoryDetail(Resource):
    @api.doc('get_product_category')
    def get(self, category_id):
        """获取单个产品分类详情"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            category_data = ProductCategorySchema().dump(category)
            return make_response(success_response, category_data, "获取产品类别详情成功")

        except Exception as e:
            current_app.logger.error(f"获取产品分类详情失败: {str(e)}")
            return make_response(error_response, f"获取产品分类详情失败: {str(e)}")

    @api.doc('update_product_category')
    def put(self, category_id):
        """更新产品分类"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            data = request.get_json() or {}
            category_schema = ProductCategorySchema(exclude=(
                'id', 'level', 'children', 'full_category_path', 'created_at', 'updated_at'
            ))

            validated_data = category_schema.load(data, partial=True)

            # 检查名称重复（如果名称或父ID改变了）
            new_name = validated_data.get('name', category.name)
            new_parent_id = validated_data.get('parent_id', category.parent_id)
            if new_name != category.name or new_parent_id != category.parent_id:
                existing = ProductCategory.query.filter(
                    ProductCategory.id != category_id,  # 排除自身
                    ProductCategory.name == new_name,
                    ProductCategory.parent_id == new_parent_id
                ).first()
                if existing:
                    return make_response(error_response, f"名称 '{new_name}' 在此级别下已存在", code=400)

            # 检查是否将父级设置为自身或子孙
            if 'parent_id' in validated_data:
                if validated_data['parent_id'] == category_id:
                    return make_response(error_response, "不能将父类别设置为自身", code=400)

                # 检查循环依赖
                parent_id_to_check = validated_data['parent_id']
                while parent_id_to_check is not None:
                    if parent_id_to_check == category_id:
                        return make_response(error_response, "不能将父类别设置为自身的子类别", code=400)
                    parent_check = ProductCategory.query.get(parent_id_to_check)
                    if not parent_check:
                        break
                    parent_id_to_check = parent_check.parent_id

            # 更新level（如果parent_id改变）
            if 'parent_id' in validated_data and validated_data['parent_id'] != category.parent_id:
                parent_level = 0
                if validated_data['parent_id']:
                    parent = ProductCategory.query.get(validated_data['parent_id'])
                    if not parent:
                        return make_response(error_response, "父类别不存在", code=400)
                    parent_level = parent.level
                validated_data['level'] = parent_level + 1

            # 更新字段
            for key, value in validated_data.items():
                setattr(category, key, value)

            db.session.commit()

            return make_response(success_response, ProductCategorySchema().dump(category), "产品类别更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            current_app.logger.error(f"更新产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"更新产品类别失败: {str(e)}")

    @api.doc('delete_product_category')
    def delete(self, category_id):
        """删除产品分类"""
        try:
            category = ProductCategory.query.get(category_id)
            if not category:
                return make_response(not_found_response, "产品分类不存在")

            # 检查是否有子类别
            if ProductCategory.query.filter_by(parent_id=category_id).first():
                return make_response(error_response, "请先删除子类别", code=400)

            # 检查是否有产品关联
            if Product.query.filter_by(category_id=category_id).first():
                return make_response(error_response, "该类别下有产品，无法删除", code=400)

            db.session.delete(category)
            db.session.commit()
            return make_response(success_response, message="产品类别删除成功")

        except Exception as e:
            current_app.logger.error(f"删除产品类别失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"删除产品类别失败: {str(e)}")


# 品牌管理相关API
@api.route('/brands')
class BrandList(Resource):
    @api.doc('get_brands')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=10)
    @api.param('name', '品牌名称搜索')
    def get(self):
        """获取品牌列表（支持分页）"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)
            name = request.args.get('name')

            query = Brand.query

            if name:
                query = query.filter(Brand.name.ilike(f'%{name}%'))

            # 根据per_page判断是否返回简化版Schema（例如，获取所有品牌用于下拉）
            if per_page == 999:  # 假设999表示获取全部
                serializer_func = lambda item: BrandSimpleSchema().dump(item)
            else:
                serializer_func = lambda item: BrandSchema().dump(item)

            # 使用分页助手
            return make_response(
                PaginationHelper.paginate_and_response,
                query=query.order_by(Brand.sort_order, Brand.name),
                serializer_func=serializer_func,
                page=page,
                per_page=per_page,
                message="获取品牌列表成功"
            )

        except Exception as e:
            current_app.logger.error(f"获取品牌列表失败: {str(e)}")
            return make_response(error_response, f"获取品牌列表失败: {str(e)}")

    @api.doc('create_brand')
    def post(self):
        """创建新品牌"""
        try:
            data = request.get_json() or {}
            brand_schema = BrandSchema(exclude=('id', 'created_at', 'updated_at'))

            validated_data = brand_schema.load(data)

            # 检查品牌名称是否已存在
            if Brand.query.filter_by(name=validated_data['name']).first():
                return make_response(error_response, "品牌名称已存在", code=400)

            brand = Brand(**validated_data)
            db.session.add(brand)
            db.session.commit()

            return make_response(success_response, BrandSchema().dump(brand), "品牌创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建品牌失败: {str(e)}")
            return make_response(error_response, "创建品牌失败", code=500)


@api.route('/brands/<int:brand_id>')
class BrandDetail(Resource):
    @api.doc('get_brand')
    def get(self, brand_id):
        """获取单个品牌详情"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            brand_data = BrandSchema().dump(brand)
            return make_response(success_response, brand_data, "获取品牌详情成功")

        except Exception as e:
            current_app.logger.error(f"获取品牌详情失败: {str(e)}")
            return make_response(error_response, f"获取品牌详情失败: {str(e)}")

    @api.doc('update_brand')
    def put(self, brand_id):
        """更新品牌信息"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            data = request.get_json() or {}
            brand_schema = BrandSchema(exclude=('id', 'created_at', 'updated_at'))

            validated_data = brand_schema.load(data, partial=True)

            # 检查如果名称被修改，新名称是否已存在
            if 'name' in validated_data and validated_data['name'] != brand.name:
                if Brand.query.filter(Brand.id != brand_id, Brand.name == validated_data['name']).first():
                    return make_response(error_response, "品牌名称已存在", code=400)

            # 更新属性
            for key, value in validated_data.items():
                setattr(brand, key, value)

            db.session.commit()
            return make_response(success_response, BrandSchema().dump(brand), "品牌更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新品牌失败: {str(e)}")
            return make_response(error_response, "更新品牌失败", code=500)

    @api.doc('delete_brand')
    def delete(self, brand_id):
        """删除品牌"""
        try:
            brand = Brand.query.get(brand_id)
            if not brand:
                return make_response(not_found_response, "品牌不存在")

            # 检查是否有产品关联
            if Product.query.filter_by(brand_id=brand_id).first():
                return make_response(error_response, "无法删除，该品牌下有关联的产品", code=400)

            db.session.delete(brand)
            db.session.commit()
            return make_response(success_response, message="品牌删除成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除品牌失败: {str(e)}")
            return make_response(error_response, "删除品牌失败", code=500)


# 产品属性管理相关API
@api.route('/<int:product_id>/attributes')
class ProductAttributeList(Resource):
    @api.doc('get_product_attributes')
    def get(self, product_id):
        """获取产品属性列表"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            attributes = ProductAttribute.query.filter_by(product_id=product_id).order_by(ProductAttribute.sort_order).all()
            attributes_data = ProductAttributeSchema(many=True).dump(attributes)

            return make_response(success_response, attributes_data, "获取产品属性列表成功")

        except Exception as e:
            current_app.logger.error(f"获取产品属性列表失败: {str(e)}")
            return make_response(error_response, f"获取产品属性列表失败: {str(e)}")

    @api.doc('create_product_attribute')
    def post(self, product_id):
        """创建产品属性"""
        try:
            product = Product.query.get(product_id)
            if not product:
                return make_response(not_found_response, "产品不存在")

            data = request.get_json() or {}
            data['product_id'] = product_id

            attribute_schema = ProductAttributeSchema(exclude=('id', 'created_at', 'updated_at'))
            validated_data = attribute_schema.load(data)

            # 检查属性名称是否已存在
            existing = ProductAttribute.query.filter_by(
                product_id=product_id,
                attribute_name=validated_data['attribute_name']
            ).first()
            if existing:
                return make_response(error_response, "该产品已存在相同名称的属性", code=400)

            attribute = ProductAttribute(**validated_data)
            db.session.add(attribute)
            db.session.commit()

            return make_response(success_response, ProductAttributeSchema().dump(attribute), "产品属性创建成功", 201)

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建产品属性失败: {str(e)}")
            return make_response(error_response, f"创建产品属性失败: {str(e)}")


@api.route('/attributes/<int:attribute_id>')
class ProductAttributeDetail(Resource):
    @api.doc('update_product_attribute')
    def put(self, attribute_id):
        """更新产品属性"""
        try:
            attribute = ProductAttribute.query.get(attribute_id)
            if not attribute:
                return make_response(not_found_response, "产品属性不存在")

            data = request.get_json() or {}
            attribute_schema = ProductAttributeSchema(exclude=('id', 'product_id', 'created_at', 'updated_at'))
            validated_data = attribute_schema.load(data, partial=True)

            # 检查属性名称是否已存在（如果名称被修改）
            if 'attribute_name' in validated_data and validated_data['attribute_name'] != attribute.attribute_name:
                existing = ProductAttribute.query.filter(
                    ProductAttribute.id != attribute_id,
                    ProductAttribute.product_id == attribute.product_id,
                    ProductAttribute.attribute_name == validated_data['attribute_name']
                ).first()
                if existing:
                    return make_response(error_response, "该产品已存在相同名称的属性", code=400)

            # 更新属性
            for key, value in validated_data.items():
                setattr(attribute, key, value)

            db.session.commit()
            return make_response(success_response, ProductAttributeSchema().dump(attribute), "产品属性更新成功")

        except ValidationError as e:
            return make_response(validation_error_response, e.messages)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新产品属性失败: {str(e)}")
            return make_response(error_response, f"更新产品属性失败: {str(e)}")

    @api.doc('delete_product_attribute')
    def delete(self, attribute_id):
        """删除产品属性"""
        try:
            attribute = ProductAttribute.query.get(attribute_id)
            if not attribute:
                return make_response(not_found_response, "产品属性不存在")

            db.session.delete(attribute)
            db.session.commit()
            return make_response(success_response, message="产品属性删除成功")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除产品属性失败: {str(e)}")
            return make_response(error_response, f"删除产品属性失败: {str(e)}")


# 产品导入导出相关API
@api.route('/export')
class ProductExport(Resource):
    @api.doc('export_products')
    @api.param('name', '产品名称搜索')
    @api.param('model', '产品型号搜索')
    @api.param('category_id', '产品分类ID', type='integer')
    @api.param('brand_id', '品牌ID', type='integer')
    @api.param('status', '产品状态')
    def get(self):
        """导出产品信息到Excel文件"""
        try:
            # 获取查询参数
            name = request.args.get('name', '')
            model = request.args.get('model', '')
            category_id = request.args.get('category_id', type=int)
            brand_id = request.args.get('brand_id', type=int)
            status = request.args.get('status', '')

            # 构建查询
            query = Product.query.options(
                selectinload(Product.category),
                selectinload(Product.brand),
                selectinload(Product.specifications)
            )

            if name:
                query = query.filter(Product.name.ilike(f'%{name}%'))
            if model:
                query = query.filter(Product.model.ilike(f'%{model}%'))
            if category_id:
                query = query.filter(Product.category_id == category_id)
            if brand_id:
                query = query.filter(Product.brand_id == brand_id)
            if status:
                query = query.filter(Product.status == status)

            products = query.order_by(Product.created_at.desc()).all()

            if not products:
                return make_response(error_response, "没有符合条件的产品可导出", code=404)

            # 准备数据
            export_data = []
            for product in products:
                product_data = {
                    '产品编号': product.id,
                    '产品名称': product.name,
                    '产品型号': product.model,
                    '产品分类': product.category.name if product.category else '',
                    '品牌': product.brand.name if product.brand else '',
                    '单位': product.unit,
                    '状态': product.status,
                    '描述': product.description or '',
                    '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else '',
                    '更新时间': product.updated_at.strftime('%Y-%m-%d %H:%M:%S') if product.updated_at else ''
                }

                # 添加规格信息
                if product.specifications:
                    for i, spec in enumerate(product.specifications):
                        if i == 0:
                            product_data['规格'] = spec.specification
                            product_data['成本价'] = float(spec.cost_price) if spec.cost_price else 0
                            product_data['建议价'] = float(spec.suggested_price) if spec.suggested_price else 0
                            product_data['税率'] = float(spec.tax_rate) if spec.tax_rate else 0
                        else:
                            # 如果有多个规格，创建新行
                            new_row = product_data.copy()
                            new_row['规格'] = spec.specification
                            new_row['成本价'] = float(spec.cost_price) if spec.cost_price else 0
                            new_row['建议价'] = float(spec.suggested_price) if spec.suggested_price else 0
                            new_row['税率'] = float(spec.tax_rate) if spec.tax_rate else 0
                            export_data.append(new_row)
                else:
                    product_data['规格'] = ''
                    product_data['成本价'] = 0
                    product_data['建议价'] = 0
                    product_data['税率'] = 0

                export_data.append(product_data)

            df = pd.DataFrame(export_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'产品列表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"导出产品失败: {str(e)}")
            return make_response(error_response, f"导出产品失败: {str(e)}")


@api.route('/export-template')
class ProductExportTemplate(Resource):
    @api.doc('get_product_export_template')
    def get(self):
        """获取产品导入模板"""
        try:
            # 创建模板数据
            template_data = {
                '产品名称': ['示例产品1', '示例产品2'],
                '产品型号': ['MODEL001', 'MODEL002'],
                '产品分类ID': [1, 1],
                '品牌ID': [1, 1],
                '单位': ['个', '套'],
                '状态': ['正常', '正常'],
                '描述': ['这是示例产品1的描述', '这是示例产品2的描述'],
                '规格': ['标准规格', '高级规格'],
                '成本价': [80.00, 120.00],
                '建议价': [100.00, 150.00],
                '税率': [13.0, 13.0]
            }

            df = pd.DataFrame(template_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品导入模板')

                # 添加说明工作表
                instructions = pd.DataFrame({
                    '字段说明': [
                        '产品名称：必填，产品的名称',
                        '产品型号：必填，产品的型号',
                        '产品分类ID：必填，产品分类的ID，请先查询分类列表获取',
                        '品牌ID：可选，品牌的ID，请先查询品牌列表获取',
                        '单位：必填，产品的计量单位',
                        '状态：可选，产品状态（正常/停用），默认为正常',
                        '描述：可选，产品的详细描述',
                        '规格：可选，产品规格描述',
                        '成本价：可选，产品成本价格',
                        '建议价：可选，产品建议销售价格',
                        '税率：可选，产品税率（百分比）'
                    ]
                })
                instructions.to_excel(writer, index=False, sheet_name='字段说明')

            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'产品导入模板_{datetime.now().strftime("%Y%m%d")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"获取产品导入模板失败: {str(e)}")
            return make_response(error_response, f"获取产品导入模板失败: {str(e)}")


@api.route('/batch-delete')
class ProductBatchDelete(Resource):
    @api.doc('batch_delete_products')
    def post(self):
        """批量删除产品"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return make_response(error_response, "请求参数错误，需要提供产品ID列表", code=400)

            # 删除指定ID的产品
            num_deleted = Product.query.filter(Product.id.in_(ids)).delete(synchronize_session=False)
            db.session.commit()

            return make_response(success_response, message=f"成功删除了 {num_deleted} 个产品")

        except Exception as e:
            current_app.logger.error(f"批量删除产品失败: {str(e)}")
            db.session.rollback()
            return make_response(error_response, f"批量删除产品失败: {str(e)}")


@api.route('/batch-export')
class ProductBatchExport(Resource):
    @api.doc('batch_export_products')
    def post(self):
        """批量导出产品信息到Excel文件"""
        try:
            data = request.get_json() or {}
            ids = data.get('ids')

            if not ids or not isinstance(ids, list):
                return make_response(error_response, "请求参数错误，需要提供产品ID列表", code=400)

            # 查询指定ID的产品
            products = Product.query.options(
                selectinload(Product.category),
                selectinload(Product.brand),
                selectinload(Product.specifications)
            ).filter(Product.id.in_(ids)).order_by(Product.created_at.desc()).all()

            if not products:
                return make_response(error_response, "没有找到指定ID的产品", code=404)

            # 准备导出数据
            export_data = []
            for product in products:
                if product.specifications:
                    # 有规格的产品，每个规格一行
                    for spec in product.specifications:
                        product_data = {
                            '产品编号': product.id,
                            '产品名称': product.name,
                            '产品型号': product.model or '',
                            '单位': product.unit or '',
                            '产品分类': product.category.name if product.category else '',
                            '品牌': product.brand.name if product.brand else '',
                            '规格名称': spec.specification or '',
                            '成本价': spec.cost_price or 0,
                            '最低售价': spec.min_price or 0,
                            '最高售价': spec.max_price or 0,
                            '建议售价': spec.suggested_price or 0,
                            '税率': spec.tax_rate or 0,
                            '状态': product.status,
                            '描述': product.description or '',
                            '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
                        }
                        export_data.append(product_data)
                else:
                    # 没有规格的产品
                    product_data = {
                        '产品编号': product.id,
                        '产品名称': product.name,
                        '产品型号': product.model or '',
                        '单位': product.unit or '',
                        '产品分类': product.category.name if product.category else '',
                        '品牌': product.brand.name if product.brand else '',
                        '规格名称': '',
                        '成本价': 0,
                        '最低售价': 0,
                        '最高售价': 0,
                        '建议售价': 0,
                        '税率': 0,
                        '状态': product.status,
                        '描述': product.description or '',
                        '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else ''
                    }
                    export_data.append(product_data)

            df = pd.DataFrame(export_data)

            # 创建Excel文件到内存
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='产品列表')
            output.seek(0)

            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'选中产品_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            )

        except Exception as e:
            current_app.logger.error(f"批量导出产品失败: {str(e)}")
            return make_response(error_response, f"批量导出产品失败: {str(e)}")


@api.route('/upload-image')
class ProductImageUpload(Resource):
    @api.doc('upload_product_image')
    def post(self):
        """上传产品图片"""
        try:
            if 'image' not in request.files:
                return make_response(error_response, "没有上传文件", code=400)

            file = request.files['image']
            if file.filename == '':
                return make_response(error_response, "没有选择文件", code=400)

            # 检查文件类型
            allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
            if not ('.' in file.filename and
                    file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
                return make_response(error_response, "不支持的文件格式，请上传png、jpg、jpeg或gif格式的图片", code=400)

            # 检查文件大小（10MB）
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头

            if file_size > 10 * 1024 * 1024:  # 10MB
                return make_response(error_response, "文件大小不能超过10MB", code=400)

            # 创建上传目录
            import os
            upload_dir = os.path.join(current_app.root_path, '..', 'uploads', 'products')
            os.makedirs(upload_dir, exist_ok=True)

            # 生成唯一文件名
            import uuid
            file_extension = file.filename.rsplit('.', 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # 保存文件
            file.save(file_path)

            # 生成访问URL (匹配前端API路径)
            file_url = f"/uploads/products/{unique_filename}"

            return make_response(success_response, {
                "url": file_url,
                "filename": unique_filename,
                "original_name": file.filename
            }, "图片上传成功")

        except Exception as e:
            current_app.logger.error(f"上传产品图片失败: {str(e)}")
            return make_response(error_response, f"上传产品图片失败: {str(e)}")


def delete_product_image_file(image_url):
    """删除产品图片文件"""
    try:
        if not image_url or not image_url.startswith('/uploads/products/'):
            return False

        # 提取文件名
        filename = image_url.replace('/uploads/products/', '')
        if not filename:
            return False

        # 构建文件路径
        upload_dir = os.path.join(current_app.root_path, '..', 'uploads', 'products')
        file_path = os.path.join(upload_dir, filename)

        # 安全检查：确保文件在指定目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(upload_dir)):
            current_app.logger.warning(f"尝试删除不安全的文件路径: {file_path}")
            return False

        # 删除文件
        if os.path.exists(file_path):
            os.remove(file_path)
            current_app.logger.info(f"成功删除图片文件: {file_path}")
            return True
        else:
            current_app.logger.warning(f"图片文件不存在: {file_path}")
            return False

    except Exception as e:
        current_app.logger.error(f"删除图片文件失败: {str(e)}")
        return False


@api.route('/preview-import')
class ProductPreviewImport(Resource):
    @api.doc('preview_product_import')
    def post(self):
        """预览产品导入数据"""
        try:
            if 'file' not in request.files:
                return make_response(error_response, "请选择要导入的文件", code=400)

            file = request.files['file']
            if file.filename == '':
                return make_response(error_response, "请选择要导入的文件", code=400)

            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                return make_response(error_response, "请上传Excel文件", code=400)

            # 读取Excel文件
            try:
                df = pd.read_excel(file)
            except Exception as e:
                return make_response(error_response, f"读取Excel文件失败: {str(e)}", code=400)

            # 验证必需列
            required_columns = ['产品名称', '产品型号', '产品分类ID', '单位']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return make_response(error_response, f"缺少必需列: {', '.join(missing_columns)}", code=400)

            # 预览数据
            preview_data = []
            errors = []

            for index, row in df.iterrows():
                row_num = index + 2  # Excel行号（从2开始，因为第1行是标题）
                row_data = {
                    'row_number': row_num,
                    'product_name': row.get('产品名称', ''),
                    'model': row.get('产品型号', ''),
                    'category_id': row.get('产品分类ID', ''),
                    'brand_id': row.get('品牌ID', ''),
                    'unit': row.get('单位', ''),
                    'status': row.get('状态', '正常'),
                    'description': row.get('描述', ''),
                    'specification': row.get('规格', ''),
                    'cost_price': row.get('成本价', 0),
                    'suggested_price': row.get('建议价', 0),
                    'tax_rate': row.get('税率', 0),
                    'errors': []
                }

                # 数据验证
                if not row_data['product_name']:
                    row_data['errors'].append('产品名称不能为空')
                if not row_data['model']:
                    row_data['errors'].append('产品型号不能为空')
                if not row_data['category_id']:
                    row_data['errors'].append('产品分类ID不能为空')
                if not row_data['unit']:
                    row_data['errors'].append('单位不能为空')

                # 检查分类是否存在
                if row_data['category_id']:
                    try:
                        category_id = int(row_data['category_id'])
                        if not ProductCategory.query.get(category_id):
                            row_data['errors'].append(f'产品分类ID {category_id} 不存在')
                    except (ValueError, TypeError):
                        row_data['errors'].append('产品分类ID必须是数字')

                # 检查品牌是否存在
                if row_data['brand_id']:
                    try:
                        brand_id = int(row_data['brand_id'])
                        if not Brand.query.get(brand_id):
                            row_data['errors'].append(f'品牌ID {brand_id} 不存在')
                    except (ValueError, TypeError):
                        row_data['errors'].append('品牌ID必须是数字')

                # 检查产品名称和型号是否重复
                existing_product = Product.query.filter_by(
                    name=row_data['product_name'],
                    model=row_data['model']
                ).first()
                if existing_product:
                    row_data['errors'].append('产品名称和型号组合已存在')

                if row_data['errors']:
                    errors.extend([f"第{row_num}行: {error}" for error in row_data['errors']])

                preview_data.append(row_data)

            result = {
                'total_rows': len(preview_data),
                'valid_rows': len([row for row in preview_data if not row['errors']]),
                'error_rows': len([row for row in preview_data if row['errors']]),
                'preview_data': preview_data[:10],  # 只返回前10行预览
                'errors': errors[:20]  # 只返回前20个错误
            }

            return make_response(success_response, result, "预览导入数据成功")

        except Exception as e:
            current_app.logger.error(f"预览产品导入失败: {str(e)}")
            return make_response(error_response, f"预览产品导入失败: {str(e)}")


@api.route('/import')
class ProductImport(Resource):
    @api.doc('import_products')
    def post(self):
        """导入产品数据"""
        try:
            if 'file' not in request.files:
                return make_response(error_response, "请选择要导入的文件", code=400)

            file = request.files['file']
            if file.filename == '':
                return make_response(error_response, "请选择要导入的文件", code=400)

            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                return make_response(error_response, "请上传Excel文件", code=400)

            # 读取Excel文件
            try:
                df = pd.read_excel(file)
            except Exception as e:
                return make_response(error_response, f"读取Excel文件失败: {str(e)}", code=400)

            # 验证必需列
            required_columns = ['产品名称', '产品型号', '产品分类ID', '单位']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return make_response(error_response, f"缺少必需列: {', '.join(missing_columns)}", code=400)

            success_count = 0
            error_count = 0
            errors = []

            for index, row in df.iterrows():
                row_num = index + 2
                try:
                    # 数据验证
                    product_name = str(row.get('产品名称', '')).strip()
                    model = str(row.get('产品型号', '')).strip()
                    category_id = row.get('产品分类ID')
                    unit = str(row.get('单位', '')).strip()

                    if not all([product_name, model, category_id, unit]):
                        errors.append(f"第{row_num}行: 必填字段不能为空")
                        error_count += 1
                        continue

                    # 检查分类是否存在
                    try:
                        category_id = int(category_id)
                        if not ProductCategory.query.get(category_id):
                            errors.append(f"第{row_num}行: 产品分类ID {category_id} 不存在")
                            error_count += 1
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"第{row_num}行: 产品分类ID必须是数字")
                        error_count += 1
                        continue

                    # 检查品牌
                    brand_id = row.get('品牌ID')
                    if brand_id:
                        try:
                            brand_id = int(brand_id)
                            if not Brand.query.get(brand_id):
                                errors.append(f"第{row_num}行: 品牌ID {brand_id} 不存在")
                                error_count += 1
                                continue
                        except (ValueError, TypeError):
                            errors.append(f"第{row_num}行: 品牌ID必须是数字")
                            error_count += 1
                            continue
                    else:
                        brand_id = None

                    # 检查产品是否已存在
                    existing_product = Product.query.filter_by(
                        name=product_name,
                        model=model
                    ).first()
                    if existing_product:
                        errors.append(f"第{row_num}行: 产品名称和型号组合已存在")
                        error_count += 1
                        continue

                    # 创建产品
                    product_data = {
                        'name': product_name,
                        'model': model,
                        'category_id': category_id,
                        'brand_id': brand_id,
                        'unit': unit,
                        'status': str(row.get('状态', '正常')).strip(),
                        'description': str(row.get('描述', '')).strip() or None
                    }

                    product = Product(**product_data)
                    db.session.add(product)
                    db.session.flush()

                    # 创建产品规格（如果有）
                    specification = str(row.get('规格', '')).strip()
                    if specification:
                        spec_data = {
                            'product_id': product.id,
                            'specification': specification,
                            'cost_price': float(row.get('成本价', 0)) or None,
                            'suggested_price': float(row.get('建议价', 0)) or None,
                            'tax_rate': float(row.get('税率', 0)) or None
                        }
                        product_spec = ProductSpecification(**spec_data)
                        db.session.add(product_spec)

                    success_count += 1

                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")
                    error_count += 1
                    continue

            db.session.commit()

            result = {
                'success_count': success_count,
                'error_count': error_count,
                'total_count': len(df),
                'errors': errors[:20]  # 只返回前20个错误
            }

            return make_response(success_response, result, f"导入完成，成功{success_count}条，失败{error_count}条")

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"导入产品失败: {str(e)}")
            return make_response(error_response, f"导入产品失败: {str(e)}")

{"name": "emb-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.ts,.tsx", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rimraf dist node_modules/.vite", "prepare": "npm run lint && npm run type-check"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.10.2", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "sass": "^1.89.2", "terser": "^5.43.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}
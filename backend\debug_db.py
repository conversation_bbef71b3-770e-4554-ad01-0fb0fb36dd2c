#!/usr/bin/env python3
"""
数据库调试脚本 - 查看订单产品来源信息
"""

import sqlite3
import json
import os

def check_database():
    """检查数据库结构和数据"""
    # 检查多个可能的数据库位置
    db_paths = [
        'instance/project.db',
        'project.db',
        'dev_project.db',
        '../instance/project.db'
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print(f"❌ 数据库文件不存在，检查了: {db_paths}")
        return

    print(f"✅ 找到数据库文件: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查看所有表
        print("\n📋 数据库表列表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"  - {table[0]}")
        else:
            print("  ❌ 数据库中没有表！")
            return
        
        # 2. 检查order_products表结构
        if any('order_products' in table[0] for table in tables):
            print("\n🔍 order_products表结构:")
            cursor.execute("PRAGMA table_info(order_products)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 3. 查询订单4、5、6的产品来源信息
        print("\n📊 订单4、5、6的产品来源信息:")
        cursor.execute("""
            SELECT
                op.id,
                op.order_id,
                op.source_type,
                op.source_id,
                op.source_display
            FROM order_products op
            WHERE op.order_id IN (4, 5, 6)
            ORDER BY op.order_id, op.id
        """)

        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"  产品ID: {row[0]}, 订单ID: {row[1]}, source_type: '{row[2]}', source_id: {row[3]}, source_display: '{row[4]}'")
        else:
            print("  ❌ 没有找到订单4、5、6的产品数据")
        
        # 4. 查询订单4、5、6的基本信息
        print("\n📋 订单4、5、6的基本信息:")
        cursor.execute("""
            SELECT
                id,
                order_number,
                quotation_ids,
                created_at,
                notes
            FROM orders
            WHERE id IN (4, 5, 6)
            ORDER BY id
        """)

        order_results = cursor.fetchall()
        if order_results:
            for row in order_results:
                print(f"  订单ID: {row[0]}, 订单号: {row[1]}, quotation_ids: '{row[2]}'")
                print(f"    创建时间: {row[3]}, 备注: '{row[4]}'")
        else:
            print("  ❌ 没有找到订单4、5、6")

        # 5. 查询报价单5、6、7的信息
        print("\n📋 报价单5、6、7的信息:")
        cursor.execute("""
            SELECT
                id,
                quotation_number,
                created_at
            FROM quotations
            WHERE id IN (5, 6, 7)
            ORDER BY id
        """)

        quotation_results = cursor.fetchall()
        if quotation_results:
            for row in quotation_results:
                print(f"  报价单ID: {row[0]}, 报价单号: {row[1]}, 创建时间: {row[2]}")
        else:
            print("  ❌ 没有找到报价单5、6、7")
        
        # 6. 查看所有订单产品的来源类型分布
        print("\n📈 所有订单产品的来源类型分布:")
        cursor.execute("""
            SELECT
                source_type,
                COUNT(*) as count
            FROM order_products
            GROUP BY source_type
        """)

        distribution = cursor.fetchall()
        for row in distribution:
            print(f"  {row[0]}: {row[1]} 个产品")

        # 7. 查看所有订单的创建方式
        print("\n📊 所有订单的创建方式:")
        cursor.execute("""
            SELECT
                id,
                order_number,
                quotation_ids,
                CASE
                    WHEN quotation_ids IS NULL OR quotation_ids = '[]' THEN '手动创建'
                    ELSE '从报价单创建'
                END as creation_type,
                created_at
            FROM orders
            ORDER BY created_at DESC
        """)

        orders = cursor.fetchall()
        for row in orders:
            print(f"  订单{row[0]} ({row[1]}): {row[3]}, quotation_ids: {row[2]}, 创建时间: {row[4]}")

        # 🔍 查询最新订单6的详细信息
        print("\n🔍 最新订单6的详细信息:")
        cursor.execute("""
            SELECT
                op.id,
                op.order_id,
                op.source_type,
                op.source_id,
                op.source_display
            FROM order_products op
            WHERE op.order_id = 6
            ORDER BY op.id
        """)

        order6_products = cursor.fetchall()
        if order6_products:
            for row in order6_products:
                print(f"  产品ID: {row[0]}")
                print(f"    source_type: '{row[2]}', source_id: {row[3]}, source_display: '{row[4]}'")
        else:
            print("  ❌ 没有找到订单6的产品数据")

        conn.close()
        
    except Exception as e:
        print(f"❌ 查询数据库时出错: {str(e)}")

if __name__ == "__main__":
    check_database()

#!/usr/bin/env python3
"""
检查客户ID 10的关联数据
"""
import sqlite3
import os

def check_customer_10_relations():
    """检查客户ID 10的关联数据"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查客户ID 10的关联数据")
        print("=" * 50)
        
        customer_id = 10
        
        # 检查客户基本信息
        cursor.execute("SELECT id, name, contact FROM customers WHERE id = ?", (customer_id,))
        customer = cursor.fetchone()
        if customer:
            print(f"客户信息: ID={customer[0]}, 名称={customer[1]}, 联系人={customer[2]}")
        else:
            print(f"❌ 客户ID {customer_id} 不存在")
            return
        
        # 检查所有可能的关联表
        tables_to_check = [
            ('quotations', 'customer_id', '报价单'),
            ('orders', 'customer_id', '订单'),
            ('quotation_requests', 'customer_id', '报价请求'),
            ('receivables', 'customer_id', '应收账款'),
            ('customer_balances', 'customer_id', '客户余额'),
            ('balance_transactions', 'customer_id', '余额交易'),
            ('receivable_details', 'customer_id', '应收明细'),
            ('statements', 'customer_id', '对账单'),
            ('statement_receivables', 'customer_id', '对账单应收'),
            ('customer_bank_accounts', 'customer_id', '银行账户'),
            ('customer_delivery_addresses', 'customer_id', '收货地址'),
        ]
        
        total_relations = 0
        
        for table, column, description in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {column} = ?", (customer_id,))
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"\n📋 {description} ({table}): {count} 条记录")
                    total_relations += count
                    
                    # 显示具体记录
                    cursor.execute(f"SELECT * FROM {table} WHERE {column} = ? LIMIT 5", (customer_id,))
                    records = cursor.fetchall()
                    for i, record in enumerate(records):
                        print(f"  {i+1}. {record}")
                        
            except Exception as e:
                print(f"❌ 检查 {table} 失败: {str(e)}")
        
        # 检查通过订单关联的发货单
        try:
            cursor.execute("""
                SELECT COUNT(*) FROM delivery_notes dn 
                JOIN orders o ON dn.order_id = o.id 
                WHERE o.customer_id = ?
            """, (customer_id,))
            delivery_count = cursor.fetchone()[0]
            
            if delivery_count > 0:
                print(f"\n📋 发货单 (通过订单关联): {delivery_count} 条记录")
                total_relations += delivery_count
                
                cursor.execute("""
                    SELECT dn.id, dn.delivery_number, dn.status, o.order_number
                    FROM delivery_notes dn 
                    JOIN orders o ON dn.order_id = o.id 
                    WHERE o.customer_id = ? LIMIT 5
                """, (customer_id,))
                deliveries = cursor.fetchall()
                for i, delivery in enumerate(deliveries):
                    print(f"  {i+1}. 发货单ID: {delivery[0]}, 编号: {delivery[1]}, 状态: {delivery[2]}, 订单: {delivery[3]}")
                    
        except Exception as e:
            print(f"❌ 检查发货单失败: {str(e)}")
        
        print(f"\n📊 总关联数据: {total_relations} 条")
        
        if total_relations == 0:
            print(f"✅ 客户ID {customer_id} 没有关联数据，应该可以删除")
        else:
            print(f"❌ 客户ID {customer_id} 有 {total_relations} 条关联数据，无法删除")
            print("\n💡 需要先删除这些关联数据才能删除客户")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查过程出错: {str(e)}")

if __name__ == "__main__":
    check_customer_10_relations()

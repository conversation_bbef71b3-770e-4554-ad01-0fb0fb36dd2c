#!/usr/bin/env python3
"""
测试客户删除功能的脚本
"""
import sqlite3
import os

def test_delete_customer():
    """测试删除客户功能"""
    db_path = r'D:\code\EMB-new\backend\instance\project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧪 测试客户删除功能")
        print("=" * 50)
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 检查外键约束状态
        cursor.execute("PRAGMA foreign_keys")
        fk_enabled = cursor.fetchone()[0]
        print(f"外键约束状态: {'启用' if fk_enabled else '禁用'}")
        
        # 选择一个测试客户（选择ID最大的，避免影响重要数据）
        cursor.execute("SELECT id, name, contact FROM customers ORDER BY id DESC LIMIT 1")
        customer = cursor.fetchone()
        
        if not customer:
            print("❌ 没有找到可测试的客户")
            return
        
        customer_id = customer[0]
        print(f"\n📋 测试客户: ID={customer_id}, 名称={customer[1]}, 联系人={customer[2]}")
        
        # 检查删除前的关联数据
        cursor.execute("SELECT COUNT(*) FROM customer_bank_accounts WHERE customer_id = ?", (customer_id,))
        bank_count_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM customer_delivery_addresses WHERE customer_id = ?", (customer_id,))
        address_count_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quotations WHERE customer_id = ?", (customer_id,))
        quotation_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders WHERE customer_id = ?", (customer_id,))
        order_count = cursor.fetchone()[0]
        
        print(f"删除前关联数据:")
        print(f"  - 银行账户: {bank_count_before} 条")
        print(f"  - 收货地址: {address_count_before} 条")
        print(f"  - 报价单: {quotation_count} 条")
        print(f"  - 订单: {order_count} 条")
        
        # 如果有业务关联数据，跳过测试
        if quotation_count > 0 or order_count > 0:
            print(f"⚠️  客户有业务关联数据，跳过删除测试")
            conn.close()
            return
        
        # 尝试删除客户
        print(f"\n🗑️  尝试删除客户ID {customer_id}...")
        
        try:
            cursor.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
            
            # 检查删除后的关联数据
            cursor.execute("SELECT COUNT(*) FROM customer_bank_accounts WHERE customer_id = ?", (customer_id,))
            bank_count_after = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM customer_delivery_addresses WHERE customer_id = ?", (customer_id,))
            address_count_after = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM customers WHERE id = ?", (customer_id,))
            customer_exists = cursor.fetchone()[0]
            
            print(f"删除后状态:")
            print(f"  - 客户存在: {'是' if customer_exists > 0 else '否'}")
            print(f"  - 银行账户: {bank_count_after} 条")
            print(f"  - 收货地址: {address_count_after} 条")
            
            if customer_exists == 0 and bank_count_after == 0 and address_count_after == 0:
                print("✅ 删除成功！级联删除正常工作")
                # 提交事务
                conn.commit()
                return True
            else:
                print("❌ 删除不完整")
                conn.rollback()
                return False
                
        except Exception as e:
            print(f"❌ 删除失败: {str(e)}")
            conn.rollback()
            return False
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_with_api():
    """使用API测试删除功能"""
    import requests
    
    print("\n🌐 使用API测试删除功能")
    print("=" * 50)
    
    # 获取客户列表
    try:
        response = requests.get('http://localhost:5001/api/v1/customers')
        if response.status_code == 200:
            customers = response.json()['data']
            
            # 找一个没有业务关联的客户
            for customer in customers:
                customer_id = customer['id']
                
                # 检查是否有业务关联（这里简化检查）
                if customer['name'].startswith('测试') or customer['name'] in ['师傅丰富的撒', '第三方']:
                    print(f"📋 尝试删除客户: ID={customer_id}, 名称={customer['name']}")
                    
                    # 发送删除请求
                    delete_response = requests.delete(f'http://localhost:5001/api/v1/customers/{customer_id}')
                    
                    print(f"API响应状态: {delete_response.status_code}")
                    print(f"API响应内容: {delete_response.text}")
                    
                    if delete_response.status_code == 200:
                        print("✅ API删除成功！")
                        return True
                    else:
                        print("❌ API删除失败")
                        continue
            
            print("⚠️  没有找到合适的测试客户")
            return False
            
        else:
            print(f"❌ 获取客户列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 先测试数据库层面
    db_success = test_delete_customer()
    
    # 如果数据库测试成功，再测试API
    if db_success:
        api_success = test_with_api()
        if api_success:
            print("\n🎉 所有测试通过！客户删除功能已修复！")
        else:
            print("\n⚠️  数据库层面正常，但API层面可能还有问题")
    else:
        print("\n❌ 数据库层面删除仍有问题")

<template>
  <div class="company-info-container">
    <el-card class="header-card">
      <div class="flex-between">
        <h2 class="form-title">企业信息</h2>
        <div>
          <el-button v-if="!isCompanyInfoEditing" type="primary" @click="isCompanyInfoEditing = true">编辑</el-button>
          <el-button v-if="!isCompanyInfoEditing" type="info" @click="fetchCompanyInfo">刷新</el-button>
          <el-button v-if="isCompanyInfoEditing" type="success" @click="saveCompanyInfo" :loading="saving.company">保存</el-button>
          <el-button v-if="isCompanyInfoEditing" @click="cancelCompanyInfoEdit">取消</el-button>
        </div>
      </div>
    </el-card>

    <el-card v-loading="loading.company" class="form-card">
      <template #header>
        <span>企业详细信息</span>
      </template>
          <el-form
            ref="companyFormRef"
            :model="companyForm"
            :rules="companyRules"
            label-width="120px"
            :disabled="!isCompanyInfoEditing"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业名称" prop="name">
                  <el-input v-model="companyForm.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码" prop="tax_id">
                  <el-input v-model="companyForm.tax_id" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系人" prop="contact">
                  <el-input v-model="companyForm.contact" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="companyForm.phone" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子邮箱" prop="email">
                  <el-input v-model="companyForm.email" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="传真" prop="fax">
                  <el-input v-model="companyForm.fax" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="公司地址" prop="address">
                  <el-input v-model="companyForm.address" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司Logo" prop="logo">
                  <el-upload
                    class="avatar-uploader"
                    action="#"
                    :show-file-list="false"
                    :http-request="handleLogoUpload"
                    :before-upload="beforeImageUpload"
                  >
                    <img v-if="companyForm.logo" :src="getImageUrl(companyForm.logo)" class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="营业执照" prop="license_image">
                  <el-upload
                    class="avatar-uploader"
                    action="#"
                    :show-file-list="false"
                    :http-request="handleLicenseUpload"
                    :before-upload="beforeImageUpload"
                  >
                    <img v-if="companyForm.license_image" :src="getImageUrl(companyForm.license_image)" class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { systemSettingsApi } from '@/api/system'


const loading = reactive({ company: false })
const saving = reactive({ company: false })

// --- 企业信息 ---
const isCompanyInfoEditing = ref(false)
const companyFormRef = ref(null)
const companyForm = reactive({
  id: null,
  name: '',
  address: '',
  contact: '',
  phone: '',
  email: '',
  fax: '',
  tax_id: '',
  logo: null,
  license_image: null
})
const companyInfoBackup = ref(null)

const companyRules = {
  name: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  address: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  contact: [
    { max: 50, message: '长度不能超过 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, message: '请输入有效的电话号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  tax_id: [
    { pattern: /^[A-Z0-9]{15,20}$/, message: '请输入有效的统一社会信用代码', trigger: 'blur' }
  ]
}

const getImageUrl = (url: string) => {
  if (!url) return ''
  if (url.startsWith('http') || url.startsWith('blob')) {
    return url
  }
  // 这里应该匹配后端服务器地址
  const baseUrl = 'http://127.0.0.1:5001'
  return url.startsWith('/') ? `${baseUrl}${url}` : `${baseUrl}/${url}`
}

const fetchCompanyInfo = async () => {
  loading.company = true
  try {
    const response = await systemSettingsApi.getCompanyInfo() as any
    console.log('获取到的企业信息:', response)
    
    // API response might be wrapped in data property
    const companyData = response.data || response
    
    // 将API返回的数据合并到表单中
    Object.keys(companyForm).forEach(key => {
      if (key in companyData) {
        companyForm[key] = companyData[key]
      }
    })
    
    // 备份初始数据，用于取消编辑
    companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
    
    // 保存到localStorage作为备份
    localStorage.setItem('companyInfo', JSON.stringify(companyForm))
    console.log('企业信息已保存到localStorage')
    
    ElMessage.success('企业信息加载成功')
  } catch (error) {
    console.error('获取企业信息失败:', error)
    
    // 尝试从localStorage恢复
    const savedInfo = localStorage.getItem('companyInfo')
    if (savedInfo) {
      try {
        const parsedInfo = JSON.parse(savedInfo)
        Object.keys(companyForm).forEach(key => {
          if (key in parsedInfo) {
            companyForm[key] = parsedInfo[key]
          }
        })
        companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
        ElMessage.info('已从本地缓存恢复企业信息')
      } catch (e) {
        console.error('解析本地缓存的企业信息失败:', e)
        ElMessage.error('获取企业信息失败，且无法从本地缓存恢复')
      }
    } else {
      // 使用模拟数据
      Object.assign(companyForm, {
        name: '某某工程物资有限公司',
        address: '北京市海淀区中关村大街1号',
        contact: '张经理',
        phone: '010-12345678',
        email: '<EMAIL>',
        fax: '010-12345679',
        tax_id: '91110000123456789X'
      })
      companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
      ElMessage.warning('获取企业信息失败，使用模拟数据')
    }
  } finally {
    loading.company = false
  }
}

const saveCompanyInfo = async () => {
  if (!companyFormRef.value) return
  
  await companyFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      saving.company = true
      try {
        // 创建FormData对象用于上传文件
        const formData = new FormData()
        
        // 添加基本字段
        Object.keys(companyForm).forEach(key => {
          if (key !== 'logo' && key !== 'license_image' && companyForm[key] !== null) {
            formData.append(key, companyForm[key])
          }
        })
        
        // 添加文件
        if (companyForm.logo instanceof File) {
          formData.append('logo', companyForm.logo)
        }
        
        if (companyForm.license_image instanceof File) {
          formData.append('license_image', companyForm.license_image)
        }
        
        const response = await systemSettingsApi.updateCompanyInfo(formData)
        console.log('保存企业信息成功:', response)
        
        // 更新本地备份
        companyInfoBackup.value = JSON.parse(JSON.stringify(companyForm))
        
        // 更新localStorage备份
        localStorage.setItem('companyInfo', JSON.stringify(companyForm))
        
        ElMessage.success('企业信息保存成功')
        isCompanyInfoEditing.value = false
      } catch (error) {
        console.error('保存企业信息失败:', error)
        ElMessage.error('保存企业信息失败')
      } finally {
        saving.company = false
      }
    } else {
      ElMessage.warning('请正确填写企业信息')
      return false
    }
  })
}

const cancelCompanyInfoEdit = () => {
  if (companyInfoBackup.value) {
    Object.keys(companyForm).forEach(key => {
      if (key in companyInfoBackup.value) {
        companyForm[key] = companyInfoBackup.value[key]
      }
    })
  }
  isCompanyInfoEditing.value = false
}

// --- 图片上传 ---
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const handleUpload = async (options: any, field: string) => {
  const { file } = options
  saving.company = true
  try {
    const response = await systemSettingsApi.uploadFile(file) as any
    // Correctly access the nested URL
    if (response && response.data && response.data.url) {
      companyForm[field] = response.data.url
      ElMessage.success('上传成功')
    } else {
      // Handle cases where the response format is not as expected
      ElMessage.error('上传失败: 服务器响应格式不正确')
    }
  } catch (error: any) {
    ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
  } finally {
    saving.company = false
  }
}

const handleLogoUpload = (options: any) => {
  handleUpload(options, 'logo')
}

const handleLicenseUpload = (options: any) => {
  handleUpload(options, 'license_image')
}

onMounted(() => {
  fetchCompanyInfo()
})
</script>

<style scoped>
.company-info-container {
  padding: 20px;
}
.header-card {
  margin-bottom: 20px;
}
.form-card {
  margin-top: 0;
}
.form-title {
  margin: 0;
  font-size: 20px;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>

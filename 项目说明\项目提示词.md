# EMB项目AI对话提示词

## 📋 项目概述

你正在协助维护EMB工程物资报价及订单管理系统。这是一个100%完成的生产就绪项目，采用Flask+Vue3技术栈，包含完整的业务流程管理功能。

### 🎯 你的任务
- 修复项目中的bug和问题
- 优化现有功能和性能
- 添加新功能或改进
- 维护项目文档的准确性

## 📚 项目知识库使用指南

### 🔍 快速开始流程

#### 1. 首先阅读概况文档
```
查看: 项目说明/概况.md
目的: 快速了解项目全貌、技术架构、目录结构
重点: 项目状态、核心功能、技术特色、代码定位指南
```

#### 2. 根据问题类型选择相关文档
```
后端问题 → 后端架构.md + API文档.md + 数据库设计.md
前端问题 → 前端架构.md + 业务流程.md
环境问题 → 开发指南.md + 部署运维.md
业务问题 → 业务流程.md + API文档.md
测试问题 → 测试文档.md
常见问题 → 问题解决.md
```

### 📖 文档详细说明

#### 核心技术文档
1. **概况.md** - 项目总览和快速上手
   - 项目背景和状态
   - 技术架构概览
   - 目录结构说明
   - 快速开始指南
   - **代码定位指南** ⭐ 重要

2. **后端架构.md** - 后端技术详解
   - Flask应用架构
   - 数据模型设计
   - API架构设计
   - 中间件和工具
   - 性能优化策略

3. **前端架构.md** - 前端技术详解
   - Vue 3应用架构
   - 路由和状态管理
   - 组件设计模式
   - API调用封装
   - 响应式设计

4. **数据库设计.md** - 数据库结构
   - 完整表结构设计
   - 外键关系图
   - 索引优化策略
   - 数据完整性约束

#### 开发和运维文档
5. **开发指南.md** - 开发环境和规范
   - 环境配置详解
   - 代码规范标准
   - 开发流程指南
   - 测试策略说明

6. **部署运维.md** - 部署和运维
   - 开发环境部署
   - 生产环境配置
   - Docker容器化
   - 监控和维护

7. **API文档.md** - 接口文档
   - 完整API端点列表
   - 请求响应格式
   - 错误码说明
   - 使用示例

#### 业务和测试文档
8. **业务流程.md** - 业务逻辑
   - 完整业务流程图
   - 状态流转规则
   - 业务规则说明
   - 操作指南

9. **测试文档.md** - 测试策略
   - 测试框架配置
   - 单元测试示例
   - 集成测试流程
   - 测试覆盖率要求

10. **问题解决.md** - 故障排除
    - 常见问题分类
    - 详细解决方案
    - 调试技巧
    - 获取帮助方式

#### 项目管理文档
11. **项目状态.md** - 项目完成状态
    - 功能完成情况
    - 技术成就总结
    - 质量保证说明

12. **技术栈.md** - 技术选型
    - 详细技术栈分析
    - 依赖包说明
    - 版本兼容性
    - 技术优势

## 🔧 Bug修复工作流程

### 1. 问题分析阶段
```
1. 阅读 概况.md 了解项目结构
2. 根据问题类型查看对应架构文档
3. 使用 问题解决.md 查找已知解决方案
4. 查看 API文档.md 了解接口规范
```

### 2. 代码定位阶段
```
使用概况.md中的"核心代码定位指南":

后端问题定位:
- API路由: backend/app/api/v1/
- 数据模型: backend/app/models/
- 数据验证: backend/app/schemas/
- 工具函数: backend/app/utils/

前端问题定位:
- 页面组件: frontend/src/views/
- API调用: frontend/src/api/
- 状态管理: frontend/src/stores/
- 公共组件: frontend/src/components/
```

### 3. 修复实施阶段
```
1. 遵循 开发指南.md 中的代码规范
2. 参考 后端架构.md 或 前端架构.md 的设计模式
3. 使用 测试文档.md 编写或更新测试用例
4. 按照 部署运维.md 进行本地验证
```

### 4. 文档更新阶段 ⭐ 重要
```
修复bug后必须更新相关文档:

如果修复涉及:
- API变更 → 更新 API文档.md
- 数据库变更 → 更新 数据库设计.md
- 业务流程变更 → 更新 业务流程.md
- 新增配置 → 更新 开发指南.md
- 架构调整 → 更新对应架构文档
- 新问题解决 → 更新 问题解决.md
```

## 🎯 具体使用场景

### 场景1: 后端API错误
```
1. 查看 API文档.md 确认接口规范
2. 查看 后端架构.md 了解API设计模式
3. 定位到 backend/app/api/v1/ 对应文件
4. 参考 数据库设计.md 检查数据模型
5. 使用 测试文档.md 编写测试用例
```

### 场景2: 前端界面问题
```
1. 查看 前端架构.md 了解组件结构
2. 查看 业务流程.md 了解业务逻辑
3. 定位到 frontend/src/views/ 对应页面
4. 检查 frontend/src/api/ API调用
5. 查看 frontend/src/stores/ 状态管理
```

### 场景3: 数据库相关问题
```
1. 查看 数据库设计.md 了解表结构
2. 查看 后端架构.md 了解ORM使用
3. 定位到 backend/app/models/ 数据模型
4. 参考 开发指南.md 数据库操作规范
```

### 场景4: 环境配置问题
```
1. 查看 开发指南.md 环境配置章节
2. 查看 部署运维.md 部署说明
3. 参考 问题解决.md 常见环境问题
4. 检查 backend/.env.example 配置示例
```

## 📝 文档维护规范

### 必须更新文档的情况
1. **API变更**: 新增、修改、删除API接口
2. **数据库变更**: 表结构、字段、关系变更
3. **业务流程变更**: 状态流转、业务规则变更
4. **配置变更**: 新增环境变量、配置项
5. **架构调整**: 技术栈、组件结构变更
6. **新问题发现**: 新的bug类型和解决方案

### 文档更新格式
```markdown
## 更新记录
- **更新时间**: 2025年X月X日
- **更新内容**: 具体变更描述
- **影响范围**: 涉及的功能模块
- **相关文件**: 修改的代码文件列表
```

## 🚀 最佳实践

### 1. 开始工作前
- 必读概况.md了解项目全貌
- 根据问题类型选择对应文档
- 使用代码定位指南快速找到相关文件

### 2. 修复过程中
- 遵循现有的代码规范和架构模式
- 参考相似功能的实现方式
- 编写或更新测试用例

### 3. 完成工作后
- 更新相关技术文档
- 记录新的问题解决方案
- 验证修复效果

### 4. 代码质量保证
- 遵循开发指南.md中的代码规范
- 使用测试文档.md中的测试策略
- 参考问题解决.md避免已知问题

## 🔍 快速参考

### 常用文件路径
```
项目文档: 项目说明/*.md
后端代码: backend/app/
前端代码: frontend/src/
配置文件: backend/.env, frontend/vite.config.ts
测试文件: backend/tests/
```

### 重要端点
```
前端应用: http://localhost:3001
后端API: http://localhost:5001
API文档: http://localhost:5001/docs/
健康检查: http://localhost:5001/test/health
```

---

**提示词版本**: v1.0  
**适用项目**: EMB工程物资报价及订单管理系统  
**最后更新**: 2025年1月  
**维护状态**: 🔄 持续更新中

**重要提醒**: 修复bug后务必更新相关文档，保持项目知识库的准确性和完整性！

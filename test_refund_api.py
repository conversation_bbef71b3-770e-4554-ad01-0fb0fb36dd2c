#!/usr/bin/env python3
"""
测试对账单退款API
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:5001/api/v1"

def test_refund_calculation():
    """测试退款金额计算"""
    print("1. 测试退款金额计算...")
    
    statement_id = 12  # 使用现有的对账单ID
    
    try:
        response = requests.get(f"{BASE_URL}/statement-refunds/statement/{statement_id}/calculate-refund")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 计算成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data['data']
        else:
            print(f"❌ 计算失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_create_refund():
    """测试创建退款记录"""
    print("\n2. 测试创建退款记录...")
    
    # 先计算可退款金额
    calc_data = test_refund_calculation()
    if not calc_data or calc_data['refund_amount'] <= 0:
        print("❌ 无可退款金额，跳过创建测试")
        return None
    
    # 创建退款记录
    refund_data = {
        "statement_id": 12,
        "refund_date": datetime.now().strftime('%Y-%m-%d'),
        "amount": min(float(calc_data['refund_amount']), 10.0),  # 退款10元或全部可退金额
        "refund_target": "direct",
        "refund_method": "bank_transfer",
        "reference_number": "TEST123456",
        "bank_account": "测试银行账户",
        "notes": "测试退款记录",
        "created_by": "测试用户"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/statement-refunds", json=refund_data)
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ 创建成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data['data']
        else:
            print(f"❌ 创建失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_list_refunds():
    """测试获取退款记录列表"""
    print("\n3. 测试获取退款记录列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/statement-refunds", params={"statement_id": 12})
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data['data']
        else:
            print(f"❌ 获取失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_balance_refund():
    """测试余额退款"""
    print("\n4. 测试余额退款...")
    
    # 创建余额退款记录
    refund_data = {
        "statement_id": 12,
        "refund_date": datetime.now().strftime('%Y-%m-%d'),
        "amount": 5.0,  # 退款5元到余额
        "refund_target": "balance",
        "refund_method": "balance",
        "notes": "测试余额退款",
        "created_by": "测试用户"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/statement-refunds", json=refund_data)
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ 余额退款创建成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data['data']
        else:
            print(f"❌ 余额退款创建失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("EMB系统 - 对账单退款API测试")
    print("=" * 60)
    
    # 测试退款金额计算
    calc_result = test_refund_calculation()
    
    # 如果有可退款金额，测试创建退款
    if calc_result and calc_result.get('needs_refund'):
        test_create_refund()
        test_balance_refund()
    
    # 测试获取退款列表
    test_list_refunds()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()

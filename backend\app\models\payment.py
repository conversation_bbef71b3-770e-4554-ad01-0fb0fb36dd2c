"""
收款逻辑相关模型
包括客户余额管理、余额交易记录、对账单收款记录
"""
from datetime import datetime
from decimal import Decimal
from app import db
from app.models.base import BaseModel


class CustomerBalance(BaseModel):
    """客户余额表 - 管理客户预付款余额"""
    __tablename__ = 'customer_balances'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, unique=True, comment='客户ID')
    balance = db.Column(db.Numeric(12, 2), nullable=False, default=0.00, comment='可用余额')
    frozen_balance = db.Column(db.Numeric(12, 2), nullable=False, default=0.00, comment='冻结余额')
    
    # 关联关系
    customer = db.relationship('Customer', backref=db.backref('balance', uselist=False, lazy=True))
    
    def __repr__(self):
        return f'<CustomerBalance {self.customer_id}: {self.balance}>'
    
    def add_balance(self, amount: Decimal, description: str = '', reference_type: str = None, reference_id: int = None):
        """增加余额"""
        if amount <= 0:
            raise ValueError("充值金额必须大于0")
        
        old_balance = self.balance
        self.balance = Decimal(str(self.balance)) + Decimal(str(amount))
        
        # 创建交易记录
        from datetime import datetime
        transaction = BalanceTransaction(
            customer_id=self.customer_id,
            transaction_type='充值',
            amount=amount,
            balance_before=old_balance,
            balance_after=self.balance,
            reference_type=reference_type,
            reference_id=reference_id,
            description=description,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.session.add(transaction)
        return transaction
    
    def deduct_balance(self, amount: Decimal, description: str = '', reference_type: str = None, reference_id: int = None):
        """扣减余额"""
        if amount <= 0:
            raise ValueError("扣减金额必须大于0")
        
        if Decimal(str(self.balance)) < Decimal(str(amount)):
            raise ValueError("余额不足")
        
        old_balance = self.balance
        self.balance = Decimal(str(self.balance)) - Decimal(str(amount))
        
        # 创建交易记录
        from datetime import datetime
        transaction = BalanceTransaction(
            customer_id=self.customer_id,
            transaction_type='消费',
            amount=-amount,  # 负数表示扣减
            balance_before=old_balance,
            balance_after=self.balance,
            reference_type=reference_type,
            reference_id=reference_id,
            description=description,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.session.add(transaction)
        return transaction
    
    def freeze_balance(self, amount: Decimal):
        """冻结余额"""
        if Decimal(str(self.balance)) < Decimal(str(amount)):
            raise ValueError("可用余额不足")
        
        self.balance = Decimal(str(self.balance)) - Decimal(str(amount))
        self.frozen_balance = Decimal(str(self.frozen_balance)) + Decimal(str(amount))
    
    def unfreeze_balance(self, amount: Decimal):
        """解冻余额"""
        if Decimal(str(self.frozen_balance)) < Decimal(str(amount)):
            raise ValueError("冻结余额不足")
        
        self.frozen_balance = Decimal(str(self.frozen_balance)) - Decimal(str(amount))
        self.balance = Decimal(str(self.balance)) + Decimal(str(amount))


class BalanceTransaction(BaseModel):
    """余额交易记录表 - 记录所有余额变动"""
    __tablename__ = 'balance_transactions'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False, comment='客户ID')
    transaction_type = db.Column(db.String(20), nullable=False, comment='交易类型：充值、消费、退款')
    amount = db.Column(db.Numeric(12, 2), nullable=False, comment='交易金额（正数为增加，负数为减少）')
    balance_before = db.Column(db.Numeric(12, 2), nullable=False, comment='交易前余额')
    balance_after = db.Column(db.Numeric(12, 2), nullable=False, comment='交易后余额')
    reference_type = db.Column(db.String(20), nullable=True, comment='关联类型：payment、statement、refund等')
    reference_id = db.Column(db.Integer, nullable=True, comment='关联记录ID')
    description = db.Column(db.String(200), nullable=True, comment='交易描述')
    
    # 关联关系
    customer = db.relationship('Customer', backref=db.backref('balance_transactions', lazy=True))
    
    def __repr__(self):
        return f'<BalanceTransaction {self.id}: {self.transaction_type} {self.amount}>'


class StatementPayment(BaseModel):
    """对账单收款记录表 - 记录对账单的收款情况"""
    __tablename__ = 'statement_payments'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    statement_id = db.Column(db.Integer, db.ForeignKey('statements.id'), nullable=False, comment='对账单ID')
    payment_date = db.Column(db.Date, nullable=False, comment='收款日期')
    amount = db.Column(db.Numeric(12, 2), nullable=False, comment='收款金额')
    payment_method = db.Column(db.String(50), nullable=False, comment='支付方式：bank_transfer、cash、online_payment等')
    payment_source = db.Column(db.String(20), nullable=False, default='direct', comment='付款来源：direct（直接付款）、balance（余额支付）')
    reference_number = db.Column(db.String(100), nullable=True, comment='交易流水号')
    bank_account = db.Column(db.String(200), nullable=True, comment='收款账户')
    balance_transaction_id = db.Column(db.Integer, db.ForeignKey('balance_transactions.id'), nullable=True, comment='关联的余额交易ID')
    notes = db.Column(db.Text, nullable=True, comment='备注')
    voucher_files = db.Column(db.Text, nullable=True, comment='收款凭据文件（JSON格式存储）')
    status = db.Column(db.String(20), nullable=False, default='已确认', comment='状态：已确认、已取消')
    created_by = db.Column(db.String(50), nullable=True, comment='创建人')
    
    # 关联关系
    balance_transaction = db.relationship('BalanceTransaction', backref=db.backref('statement_payment', uselist=False))
    
    def __repr__(self):
        return f'<StatementPayment {self.id}: Statement {self.statement_id} - {self.amount}>'
    
    @classmethod
    def create_direct_payment(cls, statement_id: int, amount: Decimal, payment_method: str,
                            payment_date: datetime = None, reference_number: str = None,
                            bank_account: str = None, notes: str = None, created_by: str = None,
                            voucher_files: str = None):
        """创建直接收款记录"""
        if payment_date is None:
            payment_date = datetime.now().date()

        payment = cls(
            statement_id=statement_id,
            payment_date=payment_date,
            amount=amount,
            payment_method=payment_method,
            payment_source='direct',
            reference_number=reference_number,
            bank_account=bank_account,
            notes=notes,
            voucher_files=voucher_files,
            created_by=created_by
        )
        return payment
    
    @classmethod
    def create_balance_payment(cls, statement_id: int, amount: Decimal, customer_id: int,
                             payment_date: datetime = None, notes: str = None, created_by: str = None):
        """创建余额支付记录"""
        if payment_date is None:
            payment_date = datetime.now().date()
        
        # 获取客户余额
        customer_balance = CustomerBalance.query.filter_by(customer_id=customer_id).first()
        if not customer_balance:
            raise ValueError("客户余额记录不存在")
        
        # 扣减余额
        transaction = customer_balance.deduct_balance(
            amount=amount,
            description=f"支付对账单 {statement_id}",
            reference_type='statement',
            reference_id=statement_id
        )
        
        # 创建收款记录
        payment = cls(
            statement_id=statement_id,
            payment_date=payment_date,
            amount=amount,
            payment_method='balance',
            payment_source='balance',
            balance_transaction_id=transaction.id,
            notes=notes,
            created_by=created_by
        )
        return payment

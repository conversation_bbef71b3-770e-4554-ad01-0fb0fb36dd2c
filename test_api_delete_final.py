#!/usr/bin/env python3
"""
最终测试API删除功能
"""
import requests
import time

def test_api_delete():
    """测试API删除功能"""
    print("🌐 测试API删除功能")
    print("=" * 50)
    
    base_url = 'http://localhost:5001/api/v1'
    
    try:
        # 1. 检查API是否可用
        print("📡 检查API连接...")
        response = requests.get(f'{base_url}/customers', timeout=5)
        if response.status_code != 200:
            print(f"❌ API连接失败: {response.status_code}")
            return False
        
        customers = response.json()['data']
        print(f"✅ API连接正常，共有 {len(customers)} 个客户")
        
        # 2. 找一个合适的测试客户
        test_customer = None
        for customer in customers:
            # 选择测试客户或者名称包含特定关键词的客户
            if (customer['name'].startswith('测试') or 
                customer['name'] in ['师傅丰富的撒', '第三方'] or
                'test' in customer['name'].lower()):
                test_customer = customer
                break
        
        if not test_customer:
            print("⚠️  没有找到合适的测试客户")
            # 创建一个测试客户
            print("📝 创建测试客户...")
            create_data = {
                "name": f"API测试客户_{int(time.time())}",
                "contact": "测试联系人",
                "phone": "13800138000",
                "level": "普通客户",
                "status": "正常"
            }
            
            create_response = requests.post(f'{base_url}/customers', json=create_data)
            if create_response.status_code == 201:
                test_customer = create_response.json()['data']
                print(f"✅ 创建测试客户成功: {test_customer['name']}")
            else:
                print(f"❌ 创建测试客户失败: {create_response.status_code}")
                return False
        
        customer_id = test_customer['id']
        customer_name = test_customer['name']
        
        print(f"\n📋 测试删除客户:")
        print(f"   ID: {customer_id}")
        print(f"   名称: {customer_name}")
        print(f"   联系人: {test_customer.get('contact', 'N/A')}")
        
        # 3. 发送删除请求
        print(f"\n🗑️  发送删除请求...")
        delete_response = requests.delete(f'{base_url}/customers/{customer_id}')
        
        print(f"API响应状态: {delete_response.status_code}")
        
        if delete_response.status_code == 200:
            result = delete_response.json()
            print(f"✅ 删除成功!")
            print(f"   响应消息: {result.get('message', '删除成功')}")
            
            # 4. 验证删除结果
            print(f"\n🔍 验证删除结果...")
            verify_response = requests.get(f'{base_url}/customers/{customer_id}')
            
            if verify_response.status_code == 404:
                print("✅ 验证成功：客户已被删除")
                return True
            else:
                print(f"❌ 验证失败：客户仍然存在 (状态码: {verify_response.status_code})")
                return False
                
        else:
            try:
                error_info = delete_response.json()
                error_message = error_info.get('message', '未知错误')
                print(f"❌ 删除失败")
                print(f"   错误信息: {error_message}")
            except:
                print(f"❌ 删除失败")
                print(f"   原始响应: {delete_response.text}")
            
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保后端服务正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ API请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        return False

def test_multiple_customers():
    """测试删除多个客户"""
    print("\n🔄 测试删除多个客户")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\n📋 测试 {i+1}/{total_tests}")
        if test_api_delete():
            success_count += 1
            print(f"✅ 测试 {i+1} 成功")
        else:
            print(f"❌ 测试 {i+1} 失败")
        
        # 短暂延迟
        time.sleep(1)
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 成功")
    return success_count == total_tests

if __name__ == "__main__":
    print("🧪 EMB客户删除功能最终测试")
    print("=" * 60)
    
    # 单次测试
    single_success = test_api_delete()
    
    if single_success:
        print("\n🎉 单次测试成功！继续进行多次测试...")
        
        # 多次测试
        multiple_success = test_multiple_customers()
        
        if multiple_success:
            print("\n🎉🎉🎉 所有测试通过！客户删除功能已完全修复！")
        else:
            print("\n⚠️  单次测试成功，但多次测试有问题")
    else:
        print("\n❌ 单次测试失败，客户删除功能仍有问题")
    
    print("\n" + "=" * 60)
    print("测试完成")

#!/usr/bin/env python3
"""
测试新的应收账款逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.finance import Statement, StatementReceivable
from app.models.customer import Customer
from datetime import datetime, date

def test_new_receivables():
    """测试新的应收账款逻辑"""
    app = create_app()
    
    with app.app_context():
        print("🧪 测试新的应收账款逻辑")
        print("=" * 50)
        
        try:
            # 1. 创建测试客户
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            customer = Customer(
                name=f"测试客户{timestamp}",
                contact="张三",
                phone="13800138000",
                email=f"test{timestamp}@example.com"
            )
            db.session.add(customer)
            db.session.flush()
            
            print(f"✅ 创建测试客户: {customer.name}")
            
            # 2. 创建测试对账单（待确认状态）
            statement = Statement(
                customer_id=customer.id,
                statement_number="ST" + timestamp,
                statement_date=date.today(),
                status="待确认",
                total_amount=1000.00,
                discount_amount=50.00,
                adjusted_total_amount=950.00,
                paid_amount=0.00
            )
            db.session.add(statement)
            db.session.flush()
            
            print(f"✅ 创建测试对账单: {statement.statement_number}")
            print(f"   状态: {statement.status}")
            print(f"   调整后金额: ¥{statement.adjusted_total_amount}")
            print(f"   已付金额: ¥{statement.paid_amount}")
            
            # 3. 测试对账单确认（应该创建应收账款）
            print("\n🔄 测试对账单确认...")
            statement.confirm(due_date=date.today())
            db.session.commit()
            
            print(f"✅ 对账单确认成功")
            print(f"   新状态: {statement.status}")
            
            # 4. 检查应收账款是否创建
            receivable = StatementReceivable.query.filter_by(statement_id=statement.id).first()
            if receivable:
                print(f"✅ 应收账款创建成功")
                print(f"   应收金额: ¥{receivable.amount}")
                print(f"   已付金额: ¥{receivable.paid_amount}")
                print(f"   未付金额: ¥{receivable.outstanding_amount}")
                print(f"   状态: {receivable.status}")
                print(f"   到期日期: {receivable.due_date}")
            else:
                print("❌ 应收账款未创建")
                return
            
            # 5. 测试部分收款（应该更新应收账款）
            print("\n💰 测试部分收款 (300元)...")
            statement.add_payment(300.00)
            db.session.commit()
            
            # 刷新对象
            db.session.refresh(statement)
            db.session.refresh(receivable)
            
            print(f"✅ 部分收款成功")
            print(f"   对账单状态: {statement.status}")
            print(f"   对账单已付金额: ¥{statement.paid_amount}")
            print(f"   应收账款金额: ¥{receivable.amount}")
            print(f"   应收账款已付金额: ¥{receivable.paid_amount}")
            print(f"   应收账款未付金额: ¥{receivable.outstanding_amount}")
            print(f"   应收账款状态: {receivable.status}")
            print(f"   最后付款日期: {receivable.last_payment_date}")
            
            # 6. 测试全额收款（应该删除应收账款）
            print("\n💰 测试全额收款 (剩余650元)...")
            statement.add_payment(650.00)
            db.session.commit()
            
            # 刷新对象
            db.session.refresh(statement)
            
            print(f"✅ 全额收款成功")
            print(f"   对账单状态: {statement.status}")
            print(f"   对账单已付金额: ¥{statement.paid_amount}")
            
            # 检查应收账款是否被删除
            receivable_after_full_payment = StatementReceivable.query.filter_by(statement_id=statement.id).first()
            if receivable_after_full_payment:
                print(f"   应收账款状态: {receivable_after_full_payment.status}")
                print(f"   应收账款未付金额: ¥{receivable_after_full_payment.outstanding_amount}")
            else:
                print("✅ 应收账款记录已删除（因为全额收款）")
            
            print("\n🎉 新应收账款逻辑测试完成！")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    test_new_receivables()

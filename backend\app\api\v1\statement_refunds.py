"""
对账单退款API模块
提供对账单退款的完整功能
"""

from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from marshmallow import ValidationError
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import joinedload

from app.models.finance import Statement, StatementRefund
from app.models.payment import CustomerBalance
from app.models.customer import Customer
from app.schemas.finance import StatementRefundSchema
from app.utils.response import success_response, error_response, validation_error_response
from app import db

# 创建命名空间
api = Namespace('statement-refunds', description='对账单退款API')

# 定义请求模型
statement_refund_model = api.model('StatementRefund', {
    'statement_id': fields.Integer(required=True, description='对账单ID'),
    'refund_date': fields.Date(required=True, description='退款日期'),
    'amount': fields.Float(required=True, description='退款金额'),
    'refund_method': fields.String(required=True, description='退款方式', enum=['bank_transfer', 'cash', 'balance', 'other']),
    'refund_target': fields.String(required=True, description='退款目标', enum=['direct', 'balance']),
    'reference_number': fields.String(description='交易流水号'),
    'bank_account': fields.String(description='退款账户'),
    'notes': fields.String(description='备注'),
    'created_by': fields.String(description='创建人')
})

@api.route('')
class StatementRefundList(Resource):
    @api.doc('list_statement_refunds')
    @api.param('statement_id', '对账单ID', type='integer')
    @api.param('status', '退款状态', type='string')
    @api.param('page', '页码', type='integer', default=1)
    @api.param('per_page', '每页数量', type='integer', default=20)
    def get(self):
        """获取对账单退款记录列表"""
        try:
            # 获取查询参数
            statement_id = request.args.get('statement_id', type=int)
            status = request.args.get('status')
            page = request.args.get('page', 1, type=int)
            per_page = min(request.args.get('per_page', 20, type=int), 100)

            # 构建查询，预加载关联关系
            query = StatementRefund.query.options(
                joinedload(StatementRefund.statement).joinedload(Statement.customer)
            )

            if statement_id:
                query = query.filter(StatementRefund.statement_id == statement_id)

            if status:
                query = query.filter(StatementRefund.status == status)

            # 分页查询
            pagination = query.order_by(StatementRefund.created_at.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

            # 序列化数据
            refunds = []

            for refund in pagination.items:
                try:
                    # 手动构建数据，避免序列化问题
                    refund_data = {
                        'id': refund.id,
                        'refund_number': refund.refund_number,
                        'statement_id': refund.statement_id,
                        'refund_date': refund.refund_date.isoformat() if refund.refund_date else None,
                        'amount': str(refund.amount),
                        'refund_method': refund.refund_method,
                        'refund_target': refund.refund_target,
                        'reference_number': refund.reference_number,
                        'bank_account': refund.bank_account,
                        'notes': refund.notes,
                        'status': refund.status,
                        'created_by': refund.created_by,
                        'created_at': refund.created_at.isoformat() if refund.created_at else None,
                        'updated_at': refund.updated_at.isoformat() if refund.updated_at else None
                    }

                    # 添加关联信息
                    if refund.statement:
                        refund_data['statement_number'] = refund.statement.statement_number
                        refund_data['customer_id'] = refund.statement.customer_id
                        if refund.statement.customer:
                            refund_data['customer_name'] = refund.statement.customer.name

                    refunds.append(refund_data)
                except Exception as e:
                    current_app.logger.error(f"序列化退款记录失败: {e}")
                    continue

            return success_response(
                data={
                    'refunds': refunds,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': pagination.total,
                        'pages': pagination.pages,
                        'has_prev': pagination.has_prev,
                        'has_next': pagination.has_next
                    }
                },
                message='获取退款记录成功'
            )

        except Exception as e:
            current_app.logger.error(f"获取退款记录失败: {str(e)}")
            return error_response(f"获取退款记录失败: {str(e)}", code=500)

    @api.doc('create_statement_refund')
    @api.expect(statement_refund_model)
    def post(self):
        """创建对账单退款记录"""
        try:
            data = request.get_json() or {}

            # 数据验证
            refund_schema = StatementRefundSchema(exclude=(
                'id', 'refund_number', 'balance_transaction_id', 'voucher_files',
                'status', 'created_at', 'updated_at', 'statement_number', 'customer_id', 'customer_name'
            ))
            validated_data = refund_schema.load(data)

            # 检查对账单是否存在
            statement = Statement.query.get(validated_data['statement_id'])
            if not statement:
                return error_response("对账单不存在", code=404)

            # 检查退款金额是否合理
            refund_amount = Decimal(str(validated_data['amount']))
            max_refund_amount = statement.calculate_refund_amount()
            
            if refund_amount > max_refund_amount:
                return error_response(
                    f"退款金额不能超过可退款金额 ¥{max_refund_amount}",
                    code=400
                )

            if refund_amount <= 0:
                return error_response("退款金额必须大于0", code=400)

            # 根据退款目标创建退款记录
            if validated_data['refund_target'] == 'balance':
                # 退回到客户余额
                refund = StatementRefund.create_balance_refund(
                    statement_id=validated_data['statement_id'],
                    amount=refund_amount,
                    customer_id=statement.customer_id,
                    refund_date=validated_data['refund_date'],
                    notes=validated_data.get('notes', ''),
                    created_by=validated_data.get('created_by', '')
                )
            else:
                # 直接退款
                refund = StatementRefund.create_direct_refund(
                    statement_id=validated_data['statement_id'],
                    amount=refund_amount,
                    refund_method=validated_data['refund_method'],
                    refund_date=validated_data['refund_date'],
                    reference_number=validated_data.get('reference_number', ''),
                    bank_account=validated_data.get('bank_account', ''),
                    notes=validated_data.get('notes', ''),
                    created_by=validated_data.get('created_by', '')
                )

            db.session.add(refund)

            # 更新对账单退款状态
            statement.add_refund(refund_amount)

            db.session.commit()

            # 返回创建的退款记录信息
            result = StatementRefundSchema().dump(refund)
            result['statement_number'] = statement.statement_number
            result['customer_id'] = statement.customer_id
            result['customer_name'] = statement.customer.name if statement.customer else ''

            return success_response(result, "退款记录创建成功", code=201)

        except ValidationError as e:
            current_app.logger.error(f"数据验证失败: {e.messages}")
            return validation_error_response(e.messages)
        except Exception as e:
            current_app.logger.error(f"创建退款记录失败: {str(e)}")
            db.session.rollback()
            return error_response(f"创建退款记录失败: {str(e)}", code=500)


@api.route('/<int:refund_id>')
class StatementRefundDetail(Resource):
    @api.doc('get_statement_refund')
    def get(self, refund_id):
        """获取退款记录详情"""
        try:
            refund = StatementRefund.query.get_or_404(refund_id)
            
            # 序列化数据
            result = StatementRefundSchema().dump(refund)
            
            # 添加关联信息
            if refund.statement:
                result['statement_number'] = refund.statement.statement_number
                result['customer_id'] = refund.statement.customer_id
                if refund.statement.customer:
                    result['customer_name'] = refund.statement.customer.name

            return success_response(result, "获取退款记录成功")

        except Exception as e:
            current_app.logger.error(f"获取退款记录失败: {str(e)}")
            return error_response(f"获取退款记录失败: {str(e)}", code=500)

    @api.doc('update_statement_refund_status')
    @api.expect(api.model('RefundStatusUpdate', {
        'status': fields.String(required=True, description='退款状态', enum=['待处理', '已退款', '已取消']),
        'notes': fields.String(description='备注')
    }))
    def put(self, refund_id):
        """更新退款记录状态"""
        try:
            data = request.get_json() or {}
            refund = StatementRefund.query.get_or_404(refund_id)
            
            # 更新状态
            if 'status' in data:
                refund.status = data['status']
            
            if 'notes' in data:
                refund.notes = data['notes']
            
            refund.updated_at = datetime.now()
            db.session.commit()

            # 返回更新后的数据
            result = StatementRefundSchema().dump(refund)
            if refund.statement:
                result['statement_number'] = refund.statement.statement_number
                result['customer_id'] = refund.statement.customer_id
                if refund.statement.customer:
                    result['customer_name'] = refund.statement.customer.name

            return success_response(result, "退款记录更新成功")

        except Exception as e:
            current_app.logger.error(f"更新退款记录失败: {str(e)}")
            db.session.rollback()
            return error_response(f"更新退款记录失败: {str(e)}", code=500)


@api.route('/statement/<int:statement_id>/calculate-refund')
class StatementRefundCalculation(Resource):
    @api.doc('calculate_statement_refund')
    def get(self, statement_id):
        """计算对账单可退款金额"""
        try:
            statement = Statement.query.get_or_404(statement_id)
            
            refund_amount = statement.calculate_refund_amount()
            needs_refund = statement.needs_refund()
            
            return success_response({
                'statement_id': statement_id,
                'statement_number': statement.statement_number,
                'total_amount': float(statement.calculate_adjusted_total_amount()),
                'paid_amount': float(statement.paid_amount or 0),
                'refund_amount': float(refund_amount),
                'needs_refund': needs_refund
            }, "计算退款金额成功")

        except Exception as e:
            current_app.logger.error(f"计算退款金额失败: {str(e)}")
            return error_response(f"计算退款金额失败: {str(e)}", code=500)

#!/usr/bin/env python3
"""
测试退货单对账修复
验证已签收的退货单能否被对账单获取
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:5001/api/v1"

def test_get_available_return_orders():
    """测试获取可对账的退货单"""
    print("1. 测试获取可对账的退货单...")
    
    # 华为技术有限公司的客户ID是1
    customer_id = 1
    
    try:
        response = requests.get(f"{BASE_URL}/statements/available-return-orders", params={
            "customer_id": customer_id
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查是否包含RT20250717E08DFE0C
            return_orders = data.get('data', {}).get('return_orders', [])
            found_target = False
            for ro in return_orders:
                if ro.get('return_number') == 'RT20250717E08DFE0C':
                    found_target = True
                    print(f"🎯 找到目标退货单: {ro['return_number']}, 状态: {ro['status']}")
                    break
            
            if not found_target:
                print("❌ 未找到退货单RT20250717E08DFE0C")
            
            return data['data']
        else:
            print(f"❌ 获取失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_get_return_order_detail():
    """测试获取退货单详情"""
    print("\n2. 测试获取退货单详情...")
    
    try:
        # 根据之前的查询，退货单ID是6
        response = requests.get(f"{BASE_URL}/returns/6")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data['data']
        else:
            print(f"❌ 获取失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("EMB系统 - 退货单对账修复测试")
    print("=" * 60)
    
    # 测试获取可对账的退货单
    available_returns = test_get_available_return_orders()
    
    # 测试获取退货单详情
    return_detail = test_get_return_order_detail()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    
    if available_returns:
        return_orders = available_returns.get('return_orders', [])
        print(f"✅ 可对账退货单数量: {len(return_orders)}")
        
        target_found = any(ro.get('return_number') == 'RT20250717E08DFE0C' for ro in return_orders)
        if target_found:
            print("✅ 退货单RT20250717E08DFE0C现在可以被对账单获取")
        else:
            print("❌ 退货单RT20250717E08DFE0C仍然无法被对账单获取")
    else:
        print("❌ 无法获取可对账退货单列表")
    
    if return_detail:
        print(f"✅ 退货单详情获取成功，状态: {return_detail.get('status')}")
    else:
        print("❌ 无法获取退货单详情")

if __name__ == "__main__":
    main()

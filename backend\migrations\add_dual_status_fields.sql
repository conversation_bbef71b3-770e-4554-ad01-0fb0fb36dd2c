-- 订单状态系统重构：添加双状态字段
-- 执行时间：2025-01-01
-- 说明：将订单状态分离为物流状态和财务状态

-- 1. 添加新的状态字段
ALTER TABLE orders ADD COLUMN order_status VARCHAR(20) DEFAULT '待确认' COMMENT '物流状态';
ALTER TABLE orders ADD COLUMN payment_status VARCHAR(20) DEFAULT '未收款' COMMENT '财务状态';

-- 2. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);

-- 3. 验证字段添加成功
.schema orders

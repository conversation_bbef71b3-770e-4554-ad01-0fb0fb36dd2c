# EMB项目文档说明

本目录包含EMB工程物资报价及订单管理系统的完整技术文档，专为AI对话优化的项目知识库。

## 🤖 新AI对话快速开始

### 第一步：必读文档
1. **概况.md** - 项目全貌和代码定位指南 (必读)
2. **快速参考.md** - 常用信息和工作流程速查
3. **项目提示词.md** - 详细的工作指南和文档使用方法

### 第二步：根据问题类型选择文档
- 🔧 **后端问题** → 后端架构.md + API文档.md
- 🎨 **前端问题** → 前端架构.md + 业务流程.md  
- 🗄️ **数据库问题** → 数据库设计.md
- ⚙️ **环境问题** → 开发指南.md + 部署运维.md
- ❓ **常见问题** → 问题解决.md
- 🧪 **测试问题** → 测试文档.md

## 📚 完整文档列表

### 🎯 AI对话专用文档
- **项目提示词.md** - 新AI对话的完整工作指南 ⭐ 重要
- **快速参考.md** - 常用信息和流程速查卡片
- **README.md** - 本文档，文档使用说明

### 📖 核心技术文档
- **概况.md** - 项目总体概况和快速上手指南
- **后端架构.md** - 后端技术架构和设计详解
- **前端架构.md** - 前端技术架构和组件设计
- **数据库设计.md** - 完整的数据库设计和表结构
- **API文档.md** - 详细的API接口文档和使用说明

### 🛠️ 开发运维文档
- **开发指南.md** - 开发环境配置和代码规范
- **部署运维.md** - 完整的部署和运维指南
- **测试文档.md** - 测试策略和测试用例说明
- **问题解决.md** - 常见问题和解决方案指南

### 📊 项目管理文档
- **项目状态.md** - 项目完成状态和成就总结
- **技术栈.md** - 详细的技术栈分析和选型说明
- **业务流程.md** - 完整的业务流程和操作指南

## 🔧 Bug修复工作流程

### 1. 快速开始 (5分钟)
```
1. 读 概况.md 了解项目结构
2. 查 快速参考.md 获取速查信息
3. 看 项目提示词.md 了解详细指南
```

### 2. 问题分析 (10分钟)
```
1. 查看 问题解决.md 寻找已知方案
2. 根据问题类型选择对应技术文档
3. 使用代码定位指南找到相关文件
```

### 3. 修复实施 (30分钟)
```
1. 遵循 开发指南.md 的代码规范
2. 参考架构文档的设计模式
3. 编写或更新测试用例
```

### 4. 更新文档 (10分钟) ⭐ 重要
```
1. 更新相关技术文档
2. 记录新的解决方案
3. 验证修复效果
```

## 📝 文档维护规范

### 必须更新文档的情况
- ✅ API变更 → 更新 API文档.md
- ✅ 数据库变更 → 更新 数据库设计.md  
- ✅ 业务流程变更 → 更新 业务流程.md
- ✅ 配置变更 → 更新 开发指南.md
- ✅ 架构调整 → 更新对应架构文档
- ✅ 新问题解决 → 更新 问题解决.md

### 文档更新格式
```markdown
## 更新记录
- **更新时间**: 2025年X月X日
- **更新内容**: 具体变更描述
- **影响范围**: 涉及的功能模块
- **相关文件**: 修改的代码文件列表
```

## 🎯 文档特色

### 为AI对话优化
- **结构化信息**: 清晰的章节和标题便于快速定位
- **代码定位指南**: 详细的文件路径和功能说明
- **实用示例**: 大量代码示例和配置模板
- **问题解决**: 常见问题的详细解决方案

### 信息完整性
- **技术架构**: 详细的前后端架构设计
- **业务流程**: 完整的业务操作流程
- **开发指南**: 实用的开发和部署指南
- **API文档**: 完整的接口文档和使用示例

## 🚀 项目概况

### 项目状态
```
✅ 项目状态: 100%完成，生产就绪
✅ 核心模块: 8个主要业务模块全部完成
✅ 技术架构: 现代化前后端分离架构
✅ 文档完整: 12个专门技术文档
✅ 测试覆盖: 功能测试和集成测试完成
```

### 技术栈
```
后端: Flask 2.3.3 + Flask-RESTX + SQLAlchemy + SQLite
前端: Vue 3 + TypeScript + Element Plus + Pinia + Vite
测试: pytest + Vitest
部署: Gunicorn + Nginx + Docker
```

---

**💡 重要提醒**: 
1. 新AI对话请先阅读 **项目提示词.md**
2. 修复bug后务必更新相关文档
3. 使用 **快速参考.md** 进行日常速查
4. 保持项目知识库的准确性和完整性

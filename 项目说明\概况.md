# EMB工程物资报价及订单管理系统 - 项目概况

## 📋 项目概述

### 项目背景
EMB系统是一个专业的工程物资报价及订单管理系统，为工程公司提供完整的业务流程管理解决方案。项目经过完全重构，采用现代化技术架构，支持从客户管理、产品管理、报价处理到订单执行、财务管理的全流程数字化管理。

### 项目状态
- **开发状态**: ✅ 已完成 (100%)
- **后端重构**: ✅ 完成 (Flask + Flask-RESTX)
- **前端重构**: ✅ 完成 (Vue 3 + TypeScript + Element Plus)
- **核心功能**: ✅ 8个主要模块全部完成
- **测试覆盖**: ✅ 功能测试和集成测试完成

### 核心功能模块
1. **工作台/仪表板** - 数据统计和图表展示
2. **客户管理** - 客户CRUD、银行账户、送货地址管理
3. **产品管理** - 产品CRUD、分类管理、品牌管理
4. **报价管理** - 报价单、需求表、模板管理
5. **订单管理** - 订单、发货单、退货、对账单管理
6. **财务管理** - 收款、退款、应收款项管理
7. **系统设置** - 企业信息、银行账户、模板配置
8. **文件管理** - 文件上传、下载、备份恢复

## 🏗️ 技术架构

### 后端架构
- **框架**: Flask 2.3.3 + Flask-RESTX
- **数据库**: SQLAlchemy ORM + SQLite
- **API文档**: Swagger/OpenAPI 3.0 自动生成
- **数据验证**: Marshmallow schemas
- **错误处理**: 统一错误处理机制
- **API基础路径**: `/api/v1`
- **服务端口**: 5001
- **文档地址**: http://localhost:5001/docs/

### 前端架构
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia + 持久化
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **构建工具**: Vite
- **开发端口**: 3001
- **API代理**: `/api` -> `http://127.0.0.1:5001`

### 数据库设计
- **数据库**: SQLite (project.db)
- **ORM**: SQLAlchemy
- **表结构**: 完全保持原项目兼容性
- **主要表**:
  - 客户相关: customers, customer_bank_accounts, customer_delivery_addresses
  - 产品相关: products, product_categories, product_specifications, brands
  - 报价相关: quotations, quotation_items, quotation_requests
  - 订单相关: orders, order_products, delivery_notes, return_orders
  - 财务相关: payment_records, statements, receivables, refund_records
  - 系统相关: system_settings, error_logs

## 📁 项目结构

```
EMB-new/
├── backend/                 # 后端应用 (Flask)
│   ├── app/                # Flask应用核心
│   │   ├── api/           # API路由模块
│   │   │   └── v1/        # API版本1
│   │   │       ├── customers.py      # 客户管理API
│   │   │       ├── products.py       # 产品管理API
│   │   │       ├── quotations.py     # 报价管理API
│   │   │       ├── orders.py         # 订单管理API
│   │   │       ├── delivery_notes.py # 发货单API
│   │   │       ├── returns.py        # 退货管理API
│   │   │       ├── statements.py     # 对账单API
│   │   │       ├── finance.py        # 财务管理API
│   │   │       ├── dashboard.py      # 工作台API
│   │   │       └── system.py         # 系统设置API
│   │   ├── models/        # 数据模型
│   │   │   ├── customer.py           # 客户模型
│   │   │   ├── product.py            # 产品模型
│   │   │   ├── quotation.py          # 报价模型
│   │   │   ├── order.py              # 订单模型
│   │   │   ├── finance.py            # 财务模型
│   │   │   └── system.py             # 系统模型
│   │   ├── schemas/       # 数据验证模式
│   │   ├── utils/         # 工具函数
│   │   └── middleware/    # 中间件
│   ├── tests/             # 测试文件
│   ├── instance/          # 数据库实例
│   ├── config.py          # 配置管理
│   ├── run.py             # 开发服务器启动
│   └── requirements.txt   # Python依赖
├── frontend/               # 前端应用 (Vue 3)
│   ├── src/
│   │   ├── api/           # API接口定义
│   │   ├── components/    # 公共组件
│   │   ├── layouts/       # 布局组件
│   │   ├── router/        # 路由配置
│   │   ├── stores/        # Pinia状态管理
│   │   ├── types/         # TypeScript类型定义
│   │   ├── utils/         # 工具函数
│   │   ├── views/         # 页面组件
│   │   │   ├── customers/     # 客户管理页面
│   │   │   ├── products/      # 产品管理页面
│   │   │   ├── quotations/    # 报价管理页面
│   │   │   ├── orders/        # 订单管理页面
│   │   │   ├── delivery/      # 发货管理页面
│   │   │   ├── finance/       # 财务管理页面
│   │   │   └── system/        # 系统设置页面
│   │   ├── App.vue        # 根组件
│   │   └── main.ts        # 入口文件
│   ├── package.json       # 前端依赖
│   └── vite.config.ts     # Vite配置
├── origin/                # 原项目代码 (参考用)
├── .taskmaster/           # TaskMaster项目管理
│   ├── docs/             # 项目文档
│   └── tasks/            # 任务管理
├── 项目说明/              # 项目说明文档
└── README.md             # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 后端启动
```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python run.py
```

### 前端启动
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 访问地址
- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:5001
- **API文档**: http://localhost:5001/docs/

## 🔧 开发指南

### API开发规范
- RESTful API设计
- 统一响应格式: `{code, message, data}`
- 错误处理: HTTP状态码 + 错误信息
- API版本控制: `/api/v1`
- 自动生成Swagger文档

### 前端开发规范
- Vue 3 Composition API
- TypeScript类型安全
- Element Plus组件库
- 响应式设计
- 路由懒加载

### 代码质量
- Python: PEP8 + 类型注解
- TypeScript: ESLint + Prettier
- 组件化开发
- 统一错误处理
- 完整的测试覆盖

## 📊 项目特色

### 技术亮点
1. **现代化技术栈**: Vue 3 + TypeScript + Flask-RESTX
2. **企业级功能**: 文件上传、数据备份、权限管理
3. **业务流程完整**: 从报价到订单到发货到财务的全流程
4. **数据安全**: 敏感信息保护、操作审计、备份恢复
5. **用户体验**: 响应式设计、加载优化、错误处理

### 业务特色
1. **客户管理**: 完整的客户信息、银行账户、送货地址管理
2. **产品管理**: 分类管理、规格管理、价格管理、图片管理
3. **报价流程**: 需求表 -> 报价单 -> 状态流转 -> 模板管理
4. **订单管理**: 订单 -> 发货单 -> 退货 -> 对账单完整流程
5. **财务管理**: 收款记录、退款处理、应收款统计、财务报表
6. **系统管理**: 企业信息、银行账户、品牌管理、数据备份

## 🎯 使用说明

### 新AI对话快速上手
1. **了解项目**: 阅读本概况文档
2. **查看代码**: 重点关注 `backend/app/` 和 `frontend/src/` 目录
3. **API文档**: 访问 http://localhost:5001/docs/ 查看完整API
4. **功能测试**: 启动前后端服务进行功能验证
5. **代码定位**: 使用codebase-retrieval工具快速找到相关代码

### 常用开发任务
- **添加新功能**: 参考现有模块结构，后端添加API，前端添加页面
- **修复问题**: 查看错误日志，定位问题代码
- **数据库操作**: 使用SQLAlchemy ORM，参考现有模型
- **前端组件**: 使用Element Plus，参考现有组件实现

## 🔍 核心代码定位指南

### 后端代码定位
```bash
# API路由定义
backend/app/api/v1/
├── customers.py      # 客户管理 API
├── products.py       # 产品管理 API
├── quotations.py     # 报价管理 API
├── orders.py         # 订单管理 API
├── delivery_notes.py # 发货单 API
├── finance.py        # 财务管理 API
└── dashboard.py      # 工作台 API

# 数据模型定义
backend/app/models/
├── customer.py       # 客户、银行账户、送货地址模型
├── product.py        # 产品、分类、规格、图片模型
├── quotation.py      # 报价单、报价项、需求表模型
├── order.py          # 订单、订单产品、发货单模型
├── finance.py        # 对账单、收款记录、应收款模型
└── system.py         # 系统设置、错误日志模型

# 数据验证模式
backend/app/schemas/  # Marshmallow验证模式
```

### 前端代码定位
```bash
# 页面组件
frontend/src/views/
├── Dashboard.vue           # 工作台首页
├── customers/             # 客户管理模块
│   ├── CustomerList.vue   # 客户列表
│   ├── CustomerForm.vue   # 客户表单
│   └── CustomerDetail.vue # 客户详情
├── products/              # 产品管理模块
├── quotations/            # 报价管理模块
├── orders/                # 订单管理模块
├── delivery/              # 发货管理模块
├── finance/               # 财务管理模块
└── system/                # 系统设置模块

# API接口定义
frontend/src/api/
├── request.ts        # Axios封装和拦截器
├── customer.ts       # 客户管理API
├── product.ts        # 产品管理API
├── order.ts          # 订单管理API
└── dashboard.ts      # 工作台API

# 状态管理
frontend/src/stores/
├── modules/
│   ├── user.ts       # 用户状态
│   ├── app.ts        # 应用设置
│   └── cache.ts      # 缓存管理
```

## 📋 API接口概览

### 主要API端点
```
# 客户管理
GET    /api/v1/customers              # 获取客户列表
POST   /api/v1/customers              # 创建客户
GET    /api/v1/customers/{id}         # 获取客户详情
PUT    /api/v1/customers/{id}         # 更新客户
DELETE /api/v1/customers/{id}         # 删除客户

# 产品管理
GET    /api/v1/products               # 获取产品列表
POST   /api/v1/products               # 创建产品
GET    /api/v1/products/categories    # 获取产品分类
GET    /api/v1/products/brands        # 获取品牌列表

# 报价管理
GET    /api/v1/quotations             # 获取报价单列表
POST   /api/v1/quotations             # 创建报价单
GET    /api/v1/quotations/requests    # 获取报价需求表

# 订单管理
GET    /api/v1/orders                 # 获取订单列表
POST   /api/v1/orders                 # 创建订单
GET    /api/v1/delivery-notes         # 获取发货单列表
GET    /api/v1/statements             # 获取对账单列表

# 财务管理
GET    /api/v1/finance/payments       # 获取收款记录
GET    /api/v1/finance/receivables    # 获取应收款项
GET    /api/v1/finance/reports        # 获取财务报表

# 工作台
GET    /api/v1/dashboard/stats        # 获取统计数据
GET    /api/v1/dashboard/charts       # 获取图表数据

# 系统管理
GET    /api/v1/system/company         # 获取企业信息
GET    /api/v1/system/settings        # 获取系统设置
```

## 🛠️ 开发环境配置

### 后端环境变量 (.env)
```bash
# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
HOST=0.0.0.0
PORT=5001

# 数据库配置
DATABASE_URL=sqlite:///project.db

# 安全配置
SECRET_KEY=your_secret_key_here

# API配置
API_TITLE=EMB System API
API_VERSION=1.0
API_DESCRIPTION=工程物资报价及订单管理系统API文档

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads
```

### 前端配置 (vite.config.ts)
```typescript
export default defineConfig({
  plugins: [vue(), vueDevTools()],
  resolve: {
    alias: { '@': fileURLToPath(new URL('./src', import.meta.url)) }
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5001',
        changeOrigin: true
      }
    }
  }
})
```

## 🧪 测试说明

### 后端测试
```bash
# 运行所有测试
cd backend
python -m pytest tests/

# 运行特定模块测试
python -m pytest tests/test_customers.py
python -m pytest tests/test_products.py
python -m pytest tests/test_orders.py
```

### 测试覆盖
- ✅ 客户管理API测试
- ✅ 产品管理API测试
- ✅ 订单管理API测试
- ✅ 发货单API测试
- ✅ 财务管理API测试
- ✅ 工作台API测试
- ✅ 集成测试

## 📦 部署说明

### 生产环境部署
```bash
# 后端部署
cd backend
pip install -r requirements.txt
gunicorn -w 4 -b 0.0.0.0:5001 run:application

# 前端构建
cd frontend
npm run build
# 将dist目录部署到Web服务器
```

### Docker部署 (可选)
```dockerfile
# 后端Dockerfile示例
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5001
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5001", "run:application"]
```

## 🔧 故障排除

### 常见问题
1. **端口冲突**: 确保5001和3001端口未被占用
2. **数据库连接**: 检查SQLite文件权限
3. **API代理**: 确认Vite代理配置正确
4. **依赖安装**: 使用正确的Python和Node.js版本

### 日志查看
```bash
# 后端日志
tail -f backend/logs/app.log

# 前端开发日志
# 在浏览器开发者工具中查看Console
```

## 🤖 新AI对话使用指南

### 快速开始
如果你是新的AI对话，请按以下步骤开始工作：

1. **首先阅读本文档** - 了解项目全貌和代码结构
2. **查看快速参考.md** - 获取常用信息和工作流程
3. **阅读项目提示词.md** - 了解详细的工作指南和文档使用方法
4. **根据问题类型选择对应的技术文档**

### 重要提醒
- 修复bug后务必更新相关文档
- 遵循现有的代码规范和架构模式
- 使用本文档的代码定位指南快速找到文件
- 保持项目知识库的准确性和完整性

---

**项目状态**: ✅ 生产就绪
**最后更新**: 2025年1月
**维护状态**: 活跃维护
**技术支持**: 通过codebase-retrieval工具快速定位相关代码

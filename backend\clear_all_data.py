#!/usr/bin/env python3
"""
清空所有订单、发货单、退货单和对账单数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.order import Order, DeliveryNote
from app.models.return_order import ReturnOrder
from app.models.finance import Statement, PaymentRecord
from sqlalchemy import text

def clear_all_data():
    """清空所有相关数据"""
    app = create_app()
    
    with app.app_context():
        print("🗑️  开始清空所有订单相关数据...")
        print("=" * 60)
        
        try:
            # 显示删除前的数据统计
            print("📊 删除前数据统计:")
            order_count = Order.query.count()
            delivery_count = DeliveryNote.query.count()
            return_count = ReturnOrder.query.count()
            statement_count = Statement.query.count()
            payment_count = PaymentRecord.query.count()
            
            print(f"   订单数量: {order_count}")
            print(f"   发货单数量: {delivery_count}")
            print(f"   退货单数量: {return_count}")
            print(f"   对账单数量: {statement_count}")
            print(f"   收款记录数量: {payment_count}")
            
            if order_count == 0 and delivery_count == 0 and return_count == 0 and statement_count == 0:
                print("\n✅ 数据库中没有需要删除的数据")
                return True
            
            # 确认删除
            print(f"\n⚠️  警告：即将删除以上所有数据！")
            confirm = input("请输入 'YES' 确认删除，或按回车取消: ")
            
            if confirm != 'YES':
                print("❌ 操作已取消")
                return False
            
            print(f"\n🔄 开始删除数据...")
            
            # 按照依赖关系的逆序删除
            
            # 1. 删除收款记录
            if payment_count > 0:
                print(f"   删除收款记录...")
                PaymentRecord.query.delete()
                print(f"   ✅ 已删除 {payment_count} 条收款记录")
            
            # 2. 删除对账单
            if statement_count > 0:
                print(f"   删除对账单...")
                Statement.query.delete()
                print(f"   ✅ 已删除 {statement_count} 个对账单")
            
            # 3. 删除退货单项目
            print(f"   删除退货单项目...")
            db.session.execute(text("DELETE FROM return_order_items"))
            
            # 4. 删除退货单
            if return_count > 0:
                print(f"   删除退货单...")
                ReturnOrder.query.delete()
                print(f"   ✅ 已删除 {return_count} 个退货单")
            
            # 5. 删除发货单项目
            print(f"   删除发货单项目...")
            db.session.execute(text("DELETE FROM delivery_note_items"))
            
            # 6. 删除发货单
            if delivery_count > 0:
                print(f"   删除发货单...")
                DeliveryNote.query.delete()
                print(f"   ✅ 已删除 {delivery_count} 个发货单")
            
            # 7. 删除订单产品
            print(f"   删除订单产品...")
            db.session.execute(text("DELETE FROM order_products"))
            
            # 8. 删除订单状态历史
            print(f"   删除订单状态历史...")
            db.session.execute(text("DELETE FROM order_status_history"))
            
            # 9. 删除订单
            if order_count > 0:
                print(f"   删除订单...")
                Order.query.delete()
                print(f"   ✅ 已删除 {order_count} 个订单")
            
            # 10. 删除其他相关表（如果存在）
            print(f"   清理其他相关数据...")
            
            # 删除对账单与发货单的关联表
            try:
                db.session.execute(text("DELETE FROM statement_delivery_notes"))
                print(f"   ✅ 已清理对账单-发货单关联")
            except Exception:
                pass  # 表可能不存在
            
            # 删除对账单与退货单的关联表
            try:
                db.session.execute(text("DELETE FROM statement_return_orders"))
                print(f"   ✅ 已清理对账单-退货单关联")
            except Exception:
                pass  # 表可能不存在
            
            # 提交所有更改
            db.session.commit()
            
            print(f"\n🎉 数据清空完成！")
            
            # 显示删除后的统计
            print(f"\n📊 删除后数据统计:")
            print(f"   订单数量: {Order.query.count()}")
            print(f"   发货单数量: {DeliveryNote.query.count()}")
            print(f"   退货单数量: {ReturnOrder.query.count()}")
            print(f"   对账单数量: {Statement.query.count()}")
            print(f"   收款记录数量: {PaymentRecord.query.count()}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 删除失败: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    clear_all_data()

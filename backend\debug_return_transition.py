#!/usr/bin/env python3
"""
调试退货单状态流转问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.api.v1.returns import RETURN_ORDER_STATUSES, STATUS_TRANSITIONS

def debug_transition():
    """调试状态流转规则"""
    print("🔍 退货单状态流转调试")
    print("=" * 50)
    
    print("📋 状态流转规则:")
    for current, allowed in STATUS_TRANSITIONS.items():
        print(f"  '{current}' -> {allowed}")
    
    print()
    print("🧪 测试具体流转:")
    current_status = "退货中"
    target_status = "已完成"
    
    print(f"当前状态: '{current_status}'")
    print(f"目标状态: '{target_status}'")
    
    allowed_statuses = STATUS_TRANSITIONS.get(current_status, [])
    print(f"允许的状态: {allowed_statuses}")
    print(f"是否允许流转: {target_status in allowed_statuses}")
    
    print()
    print("🔍 详细比较:")
    for allowed in allowed_statuses:
        match = allowed == target_status
        print(f"  '{allowed}' == '{target_status}': {match}")
        if not match:
            print(f"    允许字节: {allowed.encode('utf-8')}")
            print(f"    目标字节: {target_status.encode('utf-8')}")

if __name__ == '__main__':
    debug_transition()

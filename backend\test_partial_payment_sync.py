#!/usr/bin/env python3
"""
测试部分收款时同步更新发货单、退货单和订单状态的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.order import Order
from app.models.finance import Statement
from app.models.payment import StatementPayment
from app.models.customer import Customer
from app.models.product import Product
from decimal import Decimal
from datetime import datetime, date

def test_partial_payment_sync():
    """测试部分收款同步更新功能"""
    app = create_app()
    
    with app.app_context():
        print("🧪 测试部分收款同步更新功能")
        print("=" * 50)
        
        try:
            # 1. 创建测试客户
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            customer = Customer(
                name=f"测试客户{timestamp}",
                contact="张三",
                phone="13800138000",
                email=f"test{timestamp}@example.com"
            )
            db.session.add(customer)
            db.session.flush()
            
            # 2. 跳过产品创建，直接测试收款逻辑
            
            # 3. 创建测试订单
            order = Order(
                customer_id=customer.id,
                order_number="ORD" + datetime.now().strftime("%Y%m%d%H%M%S"),
                project_name="测试项目",
                order_status="全部发货",  # 设置为全部发货，这样全额收款后可以变为"已收款"
                payment_status="未收款",
                total_amount=1000.00,
                notes="测试订单"
            )
            db.session.add(order)
            db.session.flush()
            
            # 4. 创建发货单
            from app.models.order import DeliveryNote
            delivery_note = DeliveryNote(
                order_id=order.id,
                delivery_number="DN" + datetime.now().strftime("%Y%m%d%H%M%S"),
                delivery_date=datetime.now(),
                status="已签收",
                settlement_status="未结清",
                total_amount=1000.00,
                recipient_name="张三",
                recipient_phone="13800138000"
            )
            db.session.add(delivery_note)
            db.session.flush()
            
            # 5. 创建对账单
            statement = Statement(
                customer_id=customer.id,
                statement_number="ST" + datetime.now().strftime("%Y%m%d%H%M%S"),
                statement_date=date.today(),
                status="已确认",
                adjusted_total_amount=1000.00,
                paid_amount=0.00
            )
            db.session.add(statement)
            db.session.flush()
            
            # 6. 关联发货单到对账单
            from app.models.finance import StatementDeliveryNote
            stmt_dn = StatementDeliveryNote(
                statement_id=statement.id,
                delivery_note_id=delivery_note.id
            )
            db.session.add(stmt_dn)
            db.session.commit()
            
            print(f"✅ 创建测试数据成功")
            print(f"   订单: {order.order_number}")
            print(f"   发货单: {delivery_note.delivery_number}")
            print(f"   对账单: {statement.statement_number}")
            print()
            
            # 7. 检查初始状态
            print("📊 初始状态:")
            print(f"   订单收款状态: {order.payment_status}")
            print(f"   发货单结清状态: {delivery_note.settlement_status}")
            print(f"   对账单状态: {statement.status}")
            print(f"   对账单已付金额: {statement.paid_amount}")
            print()
            
            # 8. 测试部分收款
            print("💰 测试部分收款 (500元)...")
            statement.add_payment(500.00)
            db.session.commit()
            
            # 刷新对象状态
            db.session.refresh(order)
            db.session.refresh(delivery_note)
            db.session.refresh(statement)
            
            print("📊 部分收款后状态:")
            print(f"   订单收款状态: {order.payment_status}")
            print(f"   发货单结清状态: {delivery_note.settlement_status}")
            print(f"   对账单状态: {statement.status}")
            print(f"   对账单已付金额: {statement.paid_amount}")
            print()
            
            # 9. 测试全额收款
            print("💰 测试全额收款 (剩余500元)...")
            statement.add_payment(500.00)
            db.session.commit()
            
            # 刷新对象状态
            db.session.refresh(order)
            db.session.refresh(delivery_note)
            db.session.refresh(statement)
            
            print("📊 全额收款后状态:")
            print(f"   订单收款状态: {order.payment_status}")
            print(f"   订单物流状态: {order.order_status}")
            print(f"   发货单结清状态: {delivery_note.settlement_status}")
            print(f"   对账单状态: {statement.status}")
            print(f"   对账单已付金额: {statement.paid_amount}")
            print(f"   发货单结清日期: {delivery_note.settlement_date}")
            print()
            
            print("🎉 测试完成！")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == '__main__':
    test_partial_payment_sync()

# EMB项目测试文档

## 📋 测试概览

EMB系统采用完整的测试策略，包括单元测试、集成测试、功能测试和端到端测试，确保系统的稳定性和可靠性。

### 测试框架
- **后端测试**: pytest + Flask-Testing
- **前端测试**: Vitest + Vue Test Utils
- **API测试**: pytest + requests
- **集成测试**: pytest + SQLAlchemy
- **覆盖率工具**: pytest-cov

## 🧪 后端测试架构

### 测试配置
```python
# backend/tests/conftest.py
@pytest.fixture(scope='session')
def app():
    """创建测试应用实例"""
    # 创建临时数据库
    db_fd, db_path = tempfile.mkstemp()
    
    # 设置测试环境
    os.environ['TESTING'] = 'True'
    os.environ['DATABASE_URL'] = f'sqlite:///{db_path}'
    
    # 创建应用
    app = create_app()
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()
    
    os.close(db_fd)
    os.unlink(db_path)

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

@pytest.fixture
def db_session(app):
    """创建数据库会话"""
    with app.app_context():
        yield db.session
        db.session.rollback()
```

### 测试数据工厂
```python
# 测试数据创建
def create_test_customer(name="测试公司", contact="张三"):
    """创建测试客户"""
    customer = Customer(
        name=name,
        contact=contact,
        phone="13800138000",
        email="<EMAIL>",
        address="北京市朝阳区"
    )
    db.session.add(customer)
    db.session.commit()
    return customer

def create_test_product(name="测试产品", model="TP-001"):
    """创建测试产品"""
    category = ProductCategory(name="测试分类")
    db.session.add(category)
    db.session.commit()
    
    product = Product(
        name=name,
        model=model,
        unit="个",
        category_id=category.id
    )
    db.session.add(product)
    db.session.commit()
    return product
```

## 🔍 单元测试

### 1. 客户管理测试
```python
# backend/tests/test_customers.py
class TestCustomerAPI:
    """客户API测试类"""
    
    def test_create_customer(self, client):
        """测试创建客户"""
        data = {
            'name': '测试公司',
            'contact': '张三',
            'phone': '13800138000',
            'email': '<EMAIL>'
        }
        
        response = client.post('/api/v1/customers', json=data)
        
        assert response.status_code == 201
        assert response.json['data']['name'] == '测试公司'
    
    def test_get_customer_list(self, client):
        """测试获取客户列表"""
        # 创建测试数据
        create_test_customer()
        
        response = client.get('/api/v1/customers')
        
        assert response.status_code == 200
        assert len(response.json['data']['items']) > 0
    
    def test_update_customer(self, client):
        """测试更新客户"""
        customer = create_test_customer()
        
        data = {'name': '更新后的公司名'}
        response = client.put(f'/api/v1/customers/{customer.id}', json=data)
        
        assert response.status_code == 200
        assert response.json['data']['name'] == '更新后的公司名'
    
    def test_delete_customer(self, client):
        """测试删除客户"""
        customer = create_test_customer()
        
        response = client.delete(f'/api/v1/customers/{customer.id}')
        
        assert response.status_code == 200
        
        # 验证客户已删除
        response = client.get(f'/api/v1/customers/{customer.id}')
        assert response.status_code == 404
```

### 2. 产品管理测试
```python
# backend/tests/test_products.py
class TestProductAPI:
    """产品API测试类"""
    
    def test_create_product(self, client):
        """测试创建产品"""
        category = create_test_category()
        
        data = {
            'name': '测试产品',
            'model': 'TP-001',
            'unit': '个',
            'category_id': category.id
        }
        
        response = client.post('/api/v1/products', json=data)
        
        assert response.status_code == 201
        assert response.json['data']['name'] == '测试产品'
    
    def test_product_specifications(self, client):
        """测试产品规格管理"""
        product = create_test_product()
        
        # 添加规格
        spec_data = {
            'specification': '220V/50Hz',
            'price': 100.00
        }
        
        response = client.post(
            f'/api/v1/products/{product.id}/specifications',
            json=spec_data
        )
        
        assert response.status_code == 201
        assert response.json['data']['price'] == 100.00
```

### 3. 订单管理测试
```python
# backend/tests/test_orders.py
class TestOrderAPI:
    """订单API测试类"""
    
    def test_create_order_from_quotation(self, client):
        """测试从报价单创建订单"""
        customer = create_test_customer()
        quotation = create_test_quotation(customer.id)
        
        response = client.post(f'/api/v1/orders/from-quotation/{quotation.id}')
        
        assert response.status_code == 201
        assert response.json['data']['customer_id'] == customer.id
        assert response.json['data']['quotation_id'] == quotation.id
    
    def test_order_status_flow(self, client):
        """测试订单状态流转"""
        order = create_test_order()
        
        # 测试状态更新
        statuses = ['已确认', '生产中', '待发货', '已发货', '已完成']
        
        for status in statuses:
            response = client.put(
                f'/api/v1/orders/{order.id}/status',
                json={'status': status}
            )
            assert response.status_code == 200
            assert response.json['data']['status'] == status
```

## 🔗 集成测试

### 业务流程集成测试
```python
# backend/tests/test_integration.py
class TestBusinessFlow:
    """业务流程集成测试"""
    
    def test_complete_business_flow(self, client):
        """测试完整业务流程"""
        # 1. 创建客户
        customer_data = {
            'name': '集成测试公司',
            'contact': '李四',
            'phone': '***********'
        }
        customer_response = client.post('/api/v1/customers', json=customer_data)
        customer_id = customer_response.json['data']['id']
        
        # 2. 创建产品
        category = create_test_category()
        product_data = {
            'name': '集成测试产品',
            'model': 'ITP-001',
            'unit': '台',
            'category_id': category.id
        }
        product_response = client.post('/api/v1/products', json=product_data)
        product_id = product_response.json['data']['id']
        
        # 3. 创建报价需求
        request_data = {
            'customer_id': customer_id,
            'project_name': '集成测试项目',
            'items': [
                {
                    'product_id': product_id,
                    'quantity': 10,
                    'specification': '标准规格'
                }
            ]
        }
        request_response = client.post('/api/v1/quotations/requests', json=request_data)
        request_id = request_response.json['data']['id']
        
        # 4. 从需求生成报价单
        quotation_response = client.post(f'/api/v1/quotations/from-request/{request_id}')
        quotation_id = quotation_response.json['data']['id']
        
        # 5. 从报价单生成订单
        order_response = client.post(f'/api/v1/orders/from-quotation/{quotation_id}')
        order_id = order_response.json['data']['id']
        
        # 6. 创建发货单
        delivery_data = {
            'order_id': order_id,
            'delivery_date': '2024-01-15',
            'items': [
                {
                    'product_id': product_id,
                    'quantity': 10
                }
            ]
        }
        delivery_response = client.post('/api/v1/delivery-notes', json=delivery_data)
        
        # 7. 创建收款记录
        payment_data = {
            'order_id': order_id,
            'amount': 1000.00,
            'payment_method': 'bank_transfer',
            'payment_date': '2024-01-20'
        }
        payment_response = client.post('/api/v1/finance/payments', json=payment_data)
        
        # 验证整个流程
        assert customer_response.status_code == 201
        assert product_response.status_code == 201
        assert request_response.status_code == 201
        assert quotation_response.status_code == 201
        assert order_response.status_code == 201
        assert delivery_response.status_code == 201
        assert payment_response.status_code == 201
```

## 📊 性能测试

### API性能测试
```python
import time
import concurrent.futures

def test_api_performance(client):
    """测试API性能"""
    
    def make_request():
        return client.get('/api/v1/customers')
    
    # 并发测试
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(100)]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 验证性能指标
    assert duration < 10  # 100个请求在10秒内完成
    assert all(r.status_code == 200 for r in results)
```

## 🎯 前端测试

### 组件单元测试
```typescript
// frontend/tests/components/CustomerForm.spec.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import CustomerForm from '@/components/CustomerForm.vue'

describe('CustomerForm', () => {
  it('should render form fields correctly', () => {
    const wrapper = mount(CustomerForm)
    
    expect(wrapper.find('input[name="name"]').exists()).toBe(true)
    expect(wrapper.find('input[name="contact"]').exists()).toBe(true)
    expect(wrapper.find('input[name="phone"]').exists()).toBe(true)
  })
  
  it('should validate required fields', async () => {
    const wrapper = mount(CustomerForm)
    
    // 提交空表单
    await wrapper.find('form').trigger('submit')
    
    // 检查验证错误
    expect(wrapper.find('.error-message').text()).toContain('客户名称不能为空')
  })
  
  it('should emit save event with form data', async () => {
    const wrapper = mount(CustomerForm)
    
    // 填写表单
    await wrapper.find('input[name="name"]').setValue('测试公司')
    await wrapper.find('input[name="contact"]').setValue('张三')
    await wrapper.find('input[name="phone"]').setValue('13800138000')
    
    // 提交表单
    await wrapper.find('form').trigger('submit')
    
    // 检查事件
    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0][0]).toEqual({
      name: '测试公司',
      contact: '张三',
      phone: '13800138000'
    })
  })
})
```

## 🔧 测试工具和配置

### pytest配置
```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
```

### 测试运行命令
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_customers.py

# 运行特定测试类
pytest tests/test_customers.py::TestCustomerAPI

# 运行特定测试方法
pytest tests/test_customers.py::TestCustomerAPI::test_create_customer

# 生成覆盖率报告
pytest --cov=app --cov-report=html

# 并行运行测试
pytest -n auto
```

## 📈 测试覆盖率

### 目标覆盖率
- **整体覆盖率**: ≥ 80%
- **API接口覆盖率**: ≥ 90%
- **核心业务逻辑**: ≥ 95%
- **数据模型**: ≥ 85%

### 覆盖率报告
```bash
# 生成HTML覆盖率报告
pytest --cov=app --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

## 🚀 持续集成测试

### GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## 📝 测试最佳实践

### 1. 测试命名规范
- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试方法: `test_*`

### 2. 测试数据管理
- 使用工厂函数创建测试数据
- 每个测试独立的数据
- 测试后清理数据

### 3. 断言策略
- 明确的断言消息
- 多个小断言而非一个大断言
- 测试正常和异常情况

### 4. 测试维护
- 定期更新测试用例
- 删除过时的测试
- 保持测试代码质量

---

**测试文档版本**: v1.0  
**最后更新**: 2025年1月  
**测试框架**: pytest + Vitest  
**维护状态**: 🔄 持续更新中

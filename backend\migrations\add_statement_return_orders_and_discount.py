"""
添加对账单退货单关联表和优惠字段

Revision ID: add_statement_return_orders_and_discount
Revises: 
Create Date: 2025-01-09

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers
revision = 'add_statement_return_orders_and_discount'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """添加对账单退货单关联表和优惠字段"""
    
    # 1. 创建对账单退货单关联表
    op.create_table(
        'statement_return_orders',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('statement_id', sa.Integer(), nullable=False),
        sa.Column('return_order_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['statement_id'], ['statements.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['return_order_id'], ['return_orders.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('statement_id', 'return_order_id', name='uq_statement_return_order')
    )
    
    # 2. 为statements表添加优惠相关字段
    with op.batch_alter_table('statements', schema=None) as batch_op:
        # 添加优惠金额字段
        batch_op.add_column(sa.Column('discount_amount', sa.Numeric(precision=12, scale=2), nullable=True, default=0.0))
        # 添加调整后总金额字段
        batch_op.add_column(sa.Column('adjusted_total_amount', sa.Numeric(precision=12, scale=2), nullable=True))
    
    # 3. 更新现有数据：将adjusted_total_amount设置为total_amount
    connection = op.get_bind()
    connection.execute(
        sa.text("UPDATE statements SET adjusted_total_amount = total_amount WHERE adjusted_total_amount IS NULL")
    )
    
    # 4. 将adjusted_total_amount设置为NOT NULL
    with op.batch_alter_table('statements', schema=None) as batch_op:
        batch_op.alter_column('adjusted_total_amount', nullable=False)


def downgrade():
    """回滚操作"""
    
    # 1. 删除statements表的新增字段
    with op.batch_alter_table('statements', schema=None) as batch_op:
        batch_op.drop_column('adjusted_total_amount')
        batch_op.drop_column('discount_amount')
    
    # 2. 删除对账单退货单关联表
    op.drop_table('statement_return_orders')

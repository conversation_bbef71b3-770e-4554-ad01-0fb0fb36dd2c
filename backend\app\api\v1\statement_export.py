"""
对账单导出API模块
提供对账单的Excel和PDF导出功能
"""

from flask import request, current_app, send_file
from flask_restx import Namespace, Resource, fields
import pandas as pd
from io import BytesIO
import tempfile
import os
from datetime import datetime

from app.models.finance import Statement
from app.models.payment import StatementPayment
from app.models.order import DeliveryNote
from app.models.customer import Customer
from app.utils.response import success_response, error_response

# 创建命名空间
api = Namespace('statement-export', description='对账单导出功能API')

# 列名映射
column_mapping = {
    # 基本信息字段
    'statement_number': '对账单号',
    'customer_name': '客户名称',
    'statement_date': '对账日期',
    'due_date': '应付款日期',
    'status': '对账单状态',
    'total_amount': '总金额',
    'discount_amount': '优惠金额',
    'adjusted_total_amount': '实际金额',
    'paid_amount': '已收金额',
    'unpaid_amount': '未收金额',
    'notes': '备注',
    'created_at': '创建时间',
    
    # 发货单明细字段
    'delivery_number': '发货单号',
    'order_number': '订单号',
    'delivery_date': '发货日期',
    'delivery_status': '发货状态',
    'delivery_amount': '发货金额',
    'logistics_company': '物流公司',
    'tracking_number': '快递单号',
    
    # 收款记录字段
    'payment_date': '收款日期',
    'payment_amount': '收款金额',
    'payment_method': '支付方式',
    'payment_source': '付款来源',
    'reference_number': '交易流水号',
    'bank_account': '收款账户',
    'payment_notes': '收款备注'
}

# 字段值中文映射
value_mapping = {
    # 支付方式映射
    'payment_method': {
        'cash': '现金',
        'bank_transfer': '银行转账',
        'alipay': '支付宝',
        'wechat': '微信支付',
        'credit_card': '信用卡',
        'check': '支票',
        'other': '其他'
    },
    # 付款来源映射
    'payment_source': {
        'direct': '直接付款',
        'statement': '对账单收款',
        'advance': '预付款',
        'refund': '退款',
        'other': '其他'
    },
    # 对账单状态映射
    'status': {
        'draft': '草稿',
        'pending': '待确认',
        'confirmed': '已确认',
        'partial_paid': '部分收款',
        'paid': '已收款',
        'overdue': '逾期',
        'cancelled': '已取消'
    },
    # 发货状态映射
    'delivery_status': {
        'pending': '待发货',
        'shipped': '已发货',
        'delivered': '已签收',
        'returned': '已退货',
        'cancelled': '已取消'
    }
}

def get_display_value(field_name, value):
    """获取字段值的中文显示"""
    if not value:
        return ''

    if field_name in value_mapping:
        return value_mapping[field_name].get(value, value)
    return value

@api.route('/<int:statement_id>/export')
class StatementExport(Resource):
    @api.doc('export_statement')
    def get(self, statement_id):
        """导出对账单"""
        try:
            # 获取请求参数
            format_type = request.args.get('format', 'xlsx')
            columns = request.args.get('columns', '').split(',') if request.args.get('columns') else None
            include_header = request.args.get('include_header', 'true').lower() == 'true'

            # 查询对账单数据
            statement = Statement.query.get_or_404(statement_id)
            
            # 字段分类
            basic_fields = ['statement_number', 'customer_name', 'statement_date', 'due_date', 'status', 'total_amount', 'discount_amount', 'adjusted_total_amount', 'paid_amount', 'unpaid_amount', 'notes', 'created_at']
            delivery_fields = ['delivery_number', 'order_number', 'delivery_date', 'delivery_status', 'delivery_amount', 'logistics_company', 'tracking_number']
            payment_fields = ['payment_date', 'payment_amount', 'payment_method', 'payment_source', 'reference_number', 'bank_account', 'payment_notes']
            
            # 分离请求的字段
            requested_columns = columns or []
            basic_columns = [col for col in requested_columns if col in basic_fields]
            delivery_columns = [col for col in requested_columns if col in delivery_fields]
            payment_columns = [col for col in requested_columns if col in payment_fields]
            
            # 计算未收金额
            unpaid_amount = float(statement.adjusted_total_amount or 0) - float(statement.paid_amount or 0)
            
            # 基本信息数据
            basic_info = {
                'statement_number': statement.statement_number,
                'customer_name': statement.customer.name if statement.customer else '',
                'statement_date': statement.statement_date.strftime('%Y-%m-%d') if statement.statement_date else '',
                'due_date': statement.due_date.strftime('%Y-%m-%d') if statement.due_date else '',
                'status': get_display_value('status', statement.status),
                'total_amount': f"{float(statement.total_amount):.2f}" if statement.total_amount else '0.00',
                'discount_amount': f"{float(statement.discount_amount):.2f}" if statement.discount_amount else '0.00',
                'adjusted_total_amount': f"{float(statement.adjusted_total_amount):.2f}" if statement.adjusted_total_amount else '0.00',
                'paid_amount': f"{float(statement.paid_amount):.2f}" if statement.paid_amount else '0.00',
                'unpaid_amount': f"{unpaid_amount:.2f}",
                'notes': statement.notes or '',
                'created_at': statement.created_at.strftime('%Y-%m-%d %H:%M:%S') if statement.created_at else ''
            }

            # 准备基本信息数据（只有一行）
            basic_data = []
            if basic_columns:
                basic_row = {}
                for col in basic_columns:
                    if col in basic_info:
                        basic_row[col] = basic_info[col]
                basic_data.append(basic_row)

            # 准备发货单明细数据
            delivery_data = []
            if delivery_columns and statement.delivery_notes:
                for delivery_note in statement.delivery_notes:
                    delivery_row = {
                        'delivery_number': delivery_note.delivery_number or '',
                        'order_number': delivery_note.order.order_number if delivery_note.order else '',
                        'delivery_date': delivery_note.delivery_date.strftime('%Y-%m-%d') if delivery_note.delivery_date else '',
                        'delivery_status': get_display_value('delivery_status', delivery_note.status),
                        'delivery_amount': f"{float(delivery_note.total_amount):.2f}" if delivery_note.total_amount else '0.00',
                        'logistics_company': delivery_note.logistics_company or '',
                        'tracking_number': delivery_note.tracking_number or ''
                    }

                    # 只导出请求的发货单字段
                    filtered_delivery_row = {}
                    for col in delivery_columns:
                        if col in delivery_row:
                            filtered_delivery_row[col] = delivery_row[col]
                    delivery_data.append(filtered_delivery_row)

            # 准备收款记录数据
            payment_data = []
            if payment_columns:
                # 查询收款记录
                payment_records = StatementPayment.query.filter_by(statement_id=statement_id).all()
                for payment in payment_records:
                    payment_row = {
                        'payment_date': payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else '',
                        'payment_amount': f"{float(payment.amount):.2f}" if payment.amount else '0.00',
                        'payment_method': get_display_value('payment_method', payment.payment_method),
                        'payment_source': get_display_value('payment_source', payment.payment_source),
                        'reference_number': payment.reference_number or '',
                        'bank_account': payment.bank_account or '',
                        'payment_notes': payment.notes or ''
                    }

                    # 只导出请求的收款字段
                    filtered_payment_row = {}
                    for col in payment_columns:
                        if col in payment_row:
                            filtered_payment_row[col] = payment_row[col]
                    payment_data.append(filtered_payment_row)

            if format_type.lower() == 'xlsx':
                # Excel导出 - 分离布局
                output = BytesIO()

                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    current_row = 0
                    
                    # 写入基本信息（如果有）
                    if basic_data:
                        basic_df = pd.DataFrame(basic_data)
                        if not basic_df.empty:
                            # 重命名基本信息列
                            basic_df = basic_df.rename(columns=column_mapping)
                            # 转置基本信息，使其垂直显示
                            basic_transposed = basic_df.T.reset_index()
                            basic_transposed.columns = ['项目', '内容']
                            basic_transposed.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)
                            current_row += len(basic_transposed) + 3  # 基本信息行数 + 空行
                    
                    # 写入发货单明细（如果有）
                    if delivery_data:
                        delivery_df = pd.DataFrame(delivery_data)
                        if not delivery_df.empty:
                            # 重命名发货单明细列
                            delivery_df = delivery_df.rename(columns=column_mapping)
                            
                            # 添加发货单明细标题
                            if basic_data:  # 如果有基本信息，添加发货单明细标题
                                title_df = pd.DataFrame([['发货单明细']], columns=[''])
                                title_df.to_excel(writer, sheet_name='对账单详情', index=False, header=False, startrow=current_row)
                                current_row += 2
                            
                            # 写入发货单明细表格
                            delivery_df.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)
                            current_row += len(delivery_df) + 3  # 发货单明细行数 + 空行
                    
                    # 写入收款记录（如果有）
                    if payment_data:
                        payment_df = pd.DataFrame(payment_data)
                        if not payment_df.empty:
                            # 重命名收款记录列
                            payment_df = payment_df.rename(columns=column_mapping)
                            
                            # 添加收款记录标题
                            if basic_data or delivery_data:  # 如果有其他信息，添加收款记录标题
                                title_df = pd.DataFrame([['收款记录']], columns=[''])
                                title_df.to_excel(writer, sheet_name='对账单详情', index=False, header=False, startrow=current_row)
                                current_row += 2
                            
                            # 写入收款记录表格
                            payment_df.to_excel(writer, sheet_name='对账单详情', index=False, startrow=current_row)
                    
                    # 如果没有任何数据，创建空表格
                    if not basic_data and not delivery_data and not payment_data:
                        empty_df = pd.DataFrame([['暂无数据']], columns=['提示'])
                        empty_df.to_excel(writer, sheet_name='对账单详情', index=False)

                output.seek(0)
                
                # 生成文件名
                filename = f"对账单_{statement.statement_number}_{datetime.now().strftime('%Y%m%d')}.xlsx"
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
                temp_file.write(output.getvalue())
                temp_file.close()
                
                return send_file(
                    temp_file.name,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )

            elif format_type.lower() == 'pdf':
                # PDF导出 - 分离布局
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib import colors
                from reportlab.lib.units import inch
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                import platform

                # 注册中文字体 - 使用系统字体
                font_name = 'Helvetica'  # 默认字体
                
                try:
                    system = platform.system()
                    if system == 'Windows':
                        # Windows系统字体路径
                        font_paths = [
                            'C:/Windows/Fonts/simhei.ttf',  # 黑体
                            'C:/Windows/Fonts/simsun.ttc',  # 宋体
                            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
                            'C:/Windows/Fonts/simkai.ttf',  # 楷体
                        ]
                    elif system == 'Darwin':  # macOS
                        font_paths = [
                            '/System/Library/Fonts/PingFang.ttc',
                            '/System/Library/Fonts/STHeiti Light.ttc',
                        ]
                    else:  # Linux
                        font_paths = [
                            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                        ]
                    
                    # 尝试加载中文字体
                    for font_path in font_paths:
                        if os.path.exists(font_path):
                            try:
                                pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                                font_name = 'ChineseFont'
                                print(f"成功加载中文字体: {font_path}")
                                break
                            except Exception as e:
                                print(f"加载字体失败 {font_path}: {str(e)}")
                                continue
                    
                    # 如果没有找到合适的字体，尝试使用reportlab的内置中文字体
                    if font_name == 'Helvetica':
                        try:
                            from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                            pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                            font_name = 'STSong-Light'
                            print("使用内置中文字体: STSong-Light")
                        except Exception as e:
                            print(f"无法加载内置中文字体: {str(e)}")
                            font_name = 'Helvetica'
                                    
                except Exception as e:
                    print(f"字体加载异常: {str(e)}")
                    font_name = 'Helvetica'

                # 创建临时PDF文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
                temp_file.close()

                # 创建PDF文档
                doc = SimpleDocTemplate(temp_file.name, pagesize=A4)
                
                # 创建样式
                styles = getSampleStyleSheet()
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontName=font_name,
                    fontSize=16,
                    alignment=1,  # 居中
                    spaceAfter=20
                )
                
                section_style = ParagraphStyle(
                    'SectionTitle',
                    parent=styles['Heading2'],
                    fontName=font_name,
                    fontSize=12,
                    alignment=0,  # 左对齐
                    spaceAfter=10,
                    spaceBefore=20
                )

                story = []

                # 添加标题
                title = Paragraph(f"对账单详情 - {statement.statement_number}", title_style)
                story.append(title)
                story.append(Spacer(1, 20))

                # 添加基本信息部分
                if basic_data:
                    basic_section = Paragraph("基本信息", section_style)
                    story.append(basic_section)
                    
                    # 创建基本信息表格（两列：项目名称和内容）
                    basic_table_data = []
                    if include_header:
                        basic_table_data.append(['项目', '内容'])
                    
                    for item in basic_data:
                        for key, value in item.items():
                            display_key = column_mapping.get(key, key)
                            basic_table_data.append([display_key, str(value)])
                    
                    if basic_table_data:
                        basic_table = Table(basic_table_data, colWidths=[2*inch, 4*inch])
                        basic_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey) if include_header else ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 10),
                            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                            ('TOPPADDING', (0, 0), (-1, -1), 6),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(basic_table)
                        story.append(Spacer(1, 20))

                # 添加发货单明细部分
                if delivery_data:
                    delivery_section = Paragraph("发货单明细", section_style)
                    story.append(delivery_section)
                    
                    # 创建发货单明细表格
                    delivery_df = pd.DataFrame(delivery_data)
                    if not delivery_df.empty:
                        delivery_df = delivery_df.rename(columns=column_mapping)
                        
                        # 转换为表格数据
                        delivery_table_data = [delivery_df.columns.tolist()] if include_header else []
                        delivery_table_data.extend(delivery_df.values.tolist())

                        # 创建发货单明细表格
                        delivery_table = Table(delivery_table_data)
                        delivery_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))

                        story.append(delivery_table)
                        story.append(Spacer(1, 20))

                # 添加收款记录部分
                if payment_data:
                    payment_section = Paragraph("收款记录", section_style)
                    story.append(payment_section)
                    
                    # 创建收款记录表格
                    payment_df = pd.DataFrame(payment_data)
                    if not payment_df.empty:
                        payment_df = payment_df.rename(columns=column_mapping)
                        
                        # 转换为表格数据
                        payment_table_data = [payment_df.columns.tolist()] if include_header else []
                        payment_table_data.extend(payment_df.values.tolist())

                        # 创建收款记录表格
                        payment_table = Table(payment_table_data)
                        payment_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))

                        story.append(payment_table)

                # 构建PDF
                doc.build(story)
                
                # 生成文件名
                filename = f"对账单_{statement.statement_number}_{datetime.now().strftime('%Y%m%d')}.pdf"
                
                return send_file(
                    temp_file.name,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/pdf'
                )

            else:
                return error_response("不支持的导出格式", code=400)

        except Exception as e:
            current_app.logger.error(f"导出对账单失败: {str(e)}")
            return error_response(f"导出失败: {str(e)}", code=500)

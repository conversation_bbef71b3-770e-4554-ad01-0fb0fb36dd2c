#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单状态数据迁移脚本
将现有的单一status字段迁移到双状态系统（order_status + payment_status）
"""

import sqlite3
import sys
import os
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_db_connection():
    """获取数据库连接"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'project.db')
    return sqlite3.connect(db_path)

def migrate_status_data():
    """迁移状态数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 获取所有订单数据
        cursor.execute("""
            SELECT id, order_number, status, total_amount, paid_amount 
            FROM orders 
            WHERE order_status IS NULL OR payment_status IS NULL
        """)
        
        orders = cursor.fetchall()
        print(f"找到 {len(orders)} 个需要迁移的订单")
        
        # 状态映射规则
        status_mapping = {
            # 物流状态映射
            '待确认': ('待确认', None),
            '已确认': ('已确认', None),
            '生产中': ('生产中', None),
            '待发货': ('待发货', None),
            '发货中': ('发货中', None),
            '部分发货': ('部分发货', None),
            '全部发货': ('全部发货', None),
            '已完成': ('已完成', None),
            '已取消': ('已取消', None),
            
            # 财务相关状态需要特殊处理
            '待收款': ('全部发货', '未收款'),
            '部分收款': ('全部发货', '部分收款'),
            '已结清': ('已完成', '已收款'),
        }
        
        migrated_count = 0
        
        for order in orders:
            order_id, order_number, current_status, total_amount, paid_amount = order
            
            # 获取映射的状态
            if current_status in status_mapping:
                order_status, payment_status = status_mapping[current_status]
            else:
                # 默认映射
                order_status = current_status
                payment_status = None
            
            # 如果没有明确的财务状态，根据金额计算
            if payment_status is None:
                total = float(total_amount) if total_amount else 0
                paid = float(paid_amount) if paid_amount else 0
                
                if paid == 0:
                    payment_status = '未收款'
                elif paid < total:
                    payment_status = '部分收款'
                elif paid >= total:
                    payment_status = '已收款'
                else:
                    payment_status = '未收款'
            
            # 更新订单状态
            cursor.execute("""
                UPDATE orders 
                SET order_status = ?, payment_status = ?
                WHERE id = ?
            """, (order_status, payment_status, order_id))
            
            migrated_count += 1
            print(f"迁移订单 {order_number}: {current_status} -> 物流:{order_status}, 财务:{payment_status}")
        
        # 提交事务
        conn.commit()
        print(f"\n✅ 成功迁移 {migrated_count} 个订单的状态数据")
        
        # 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM orders WHERE order_status IS NOT NULL AND payment_status IS NOT NULL")
        total_migrated = cursor.fetchone()[0]
        print(f"✅ 验证：共有 {total_migrated} 个订单已完成状态迁移")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 迁移失败: {str(e)}")
        raise
    finally:
        conn.close()

def verify_migration():
    """验证迁移结果"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 检查迁移统计
        cursor.execute("""
            SELECT 
                order_status,
                payment_status,
                COUNT(*) as count
            FROM orders 
            GROUP BY order_status, payment_status
            ORDER BY order_status, payment_status
        """)
        
        results = cursor.fetchall()
        print("\n📊 迁移结果统计:")
        print("物流状态 | 财务状态 | 数量")
        print("-" * 40)
        for row in results:
            print(f"{row[0]} | {row[1]} | {row[2]}")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 开始订单状态数据迁移...")
    migrate_status_data()
    verify_migration()
    print("✅ 迁移完成！")
